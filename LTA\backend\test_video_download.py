#!/usr/bin/env python3
"""
Test script for video download functionality
Tests the video download API endpoint and verifies proper response headers
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_video_download_endpoint():
    """Test the video download endpoint with various scenarios"""
    
    base_url = "http://localhost:5000"  # Adjust if your server runs on different port
    
    print("🧪 Testing Video Download Functionality")
    print("=" * 50)
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f"{base_url}/api/pavement/debug-videos", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running and accessible")
            videos_data = response.json()
            if videos_data.get('success') and videos_data.get('videos'):
                print(f"📊 Found {len(videos_data['videos'])} videos in database")
                
                # Get the first video for testing
                test_video = videos_data['videos'][0]
                video_id = test_video.get('_id')
                print(f"🎬 Testing with video ID: {video_id}")
                
                # Test original video download
                test_video_download(base_url, video_id, 'original')
                
                # Test processed video download
                test_video_download(base_url, video_id, 'processed')
                
            else:
                print("⚠️  No videos found in database for testing")
        else:
            print(f"❌ Server not accessible: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to server: {e}")
        print("💡 Make sure the backend server is running on http://localhost:5000")
        return False
    
    # Test 2: Invalid video ID
    print("\n🧪 Testing invalid video ID...")
    test_video_download(base_url, "invalid_video_id", 'original', expect_error=True)
    
    # Test 3: Invalid video type
    print("\n🧪 Testing invalid video type...")
    if 'video_id' in locals():
        test_video_download(base_url, video_id, 'invalid_type', expect_error=True)
    
    print("\n✅ Video download tests completed!")
    return True

def test_video_download(base_url, video_id, video_type, expect_error=False):
    """Test downloading a specific video"""
    
    download_url = f"{base_url}/api/pavement/get-s3-video/{video_id}/{video_type}"
    print(f"\n🔍 Testing: {video_type} video download")
    print(f"📍 URL: {download_url}")
    
    try:
        # Make HEAD request first to check headers without downloading full content
        head_response = requests.head(download_url, timeout=10)
        print(f"📋 HEAD Response Status: {head_response.status_code}")
        
        if head_response.status_code == 200:
            headers = head_response.headers
            print("📄 Response Headers:")
            for key, value in headers.items():
                if key.lower() in ['content-type', 'content-disposition', 'content-length', 
                                 'accept-ranges', 'content-transfer-encoding']:
                    print(f"   {key}: {value}")
            
            # Verify important headers
            content_type = headers.get('Content-Type', '').lower()
            content_disposition = headers.get('Content-Disposition', '')
            
            if 'video/mp4' in content_type:
                print("✅ Correct Content-Type: video/mp4")
            else:
                print(f"⚠️  Unexpected Content-Type: {content_type}")
            
            if 'attachment' in content_disposition and '.mp4' in content_disposition:
                print("✅ Correct Content-Disposition with .mp4 filename")
            else:
                print(f"⚠️  Unexpected Content-Disposition: {content_disposition}")
            
            # Test actual download (first few bytes only)
            response = requests.get(download_url, stream=True, timeout=10)
            if response.status_code == 200:
                # Read first 1KB to verify it's binary data
                chunk = next(response.iter_content(chunk_size=1024), b'')
                if chunk:
                    print(f"✅ Successfully received binary data ({len(chunk)} bytes)")
                    # Check if it looks like video data (MP4 files typically start with specific bytes)
                    if chunk.startswith(b'\x00\x00\x00') or b'ftyp' in chunk[:20]:
                        print("✅ Data appears to be valid MP4 format")
                    else:
                        print("⚠️  Data may not be valid MP4 format")
                else:
                    print("⚠️  No data received")
            else:
                print(f"❌ Download failed: {response.status_code}")
                
        elif expect_error:
            print(f"✅ Expected error received: {head_response.status_code}")
        else:
            print(f"❌ Unexpected status code: {head_response.status_code}")
            # Try to get error message
            try:
                error_response = requests.get(download_url, timeout=5)
                if error_response.headers.get('content-type', '').startswith('application/json'):
                    error_data = error_response.json()
                    print(f"📝 Error message: {error_data.get('message', 'Unknown error')}")
            except:
                pass
                
    except requests.exceptions.RequestException as e:
        if expect_error:
            print(f"✅ Expected error: {e}")
        else:
            print(f"❌ Request failed: {e}")

def check_database_videos():
    """Check what videos are available in the database"""
    
    base_url = "http://localhost:5000"
    
    try:
        response = requests.get(f"{base_url}/api/pavement/debug-videos", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                videos = data.get('videos', [])
                print(f"\n📊 Database contains {len(videos)} videos:")
                for i, video in enumerate(videos[:5]):  # Show first 5 videos
                    print(f"   {i+1}. ID: {video.get('_id', 'N/A')}")
                    print(f"      Username: {video.get('username', 'N/A')}")
                    print(f"      Status: {video.get('status', 'N/A')}")
                    print(f"      Original URL: {video.get('original_video_url', 'N/A')}")
                    print(f"      Processed URL: {video.get('processed_video_url', 'N/A')}")
                    print()
                return videos
            else:
                print("❌ Failed to get video data from database")
        else:
            print(f"❌ Database query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error checking database: {e}")
    
    return []

if __name__ == "__main__":
    print("🎬 Video Download Test Suite")
    print(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # First check what videos are available
    videos = check_database_videos()
    
    if videos:
        # Run the main test suite
        success = test_video_download_endpoint()
        
        if success:
            print("\n🎉 All tests completed successfully!")
            print("\n💡 If you're still experiencing download issues:")
            print("   1. Check browser console for JavaScript errors")
            print("   2. Verify network tab shows correct Content-Type headers")
            print("   3. Try downloading directly via curl:")
            print(f"      curl -O -J '{base_url}/api/pavement/get-s3-video/{{video_id}}/original'")
        else:
            print("\n❌ Some tests failed. Check the output above for details.")
    else:
        print("\n⚠️  No videos found in database. Upload a video first to test download functionality.")
