{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LTAGIT\\\\LTA\\\\frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { Container, Row, Col, Card, Tabs, Tab, Form, Button } from 'react-bootstrap';\nimport Plot from 'react-plotly.js';\nimport ChartContainer from '../components/ChartContainer';\nimport DefectMap from '../components/DefectMap';\nimport './dashboard.css';\nimport jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\nimport * as XLSX from 'xlsx';\nimport { saveAs } from 'file-saver';\n\n/**\r\n * Comprehensive Image URL Resolution Logic\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n *\r\n * Priority order:\r\n * 1. S3 Full URL (direct HTTPS link)\r\n * 2. S3 Key (generate URL via API)\r\n * 3. GridFS ID (legacy endpoint)\r\n * 4. Fallback to \"No image available\"\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getImageUrlForDisplay = (imageData, imageType = 'original') => {\n  console.log('getImageUrlForDisplay called:', {\n    imageData,\n    imageType\n  });\n  if (!imageData) {\n    console.log('No imageData provided');\n    return null;\n  }\n\n  // Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\n  const fullUrlField = `${imageType}_image_full_url`;\n  if (imageData[fullUrlField]) {\n    console.log('Using full URL field:', fullUrlField, imageData[fullUrlField]);\n    // Extract S3 key from full URL and use proxy endpoint\n    const urlParts = imageData[fullUrlField].split('/');\n    const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\n    if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\n      const s3Key = urlParts.slice(bucketIndex + 1).join('/');\n      const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n      console.log('Generated proxy URL from full URL:', proxyUrl);\n      return proxyUrl;\n    }\n  }\n\n  // Try S3 key with proxy endpoint (new images without full URL)\n  const s3KeyField = `${imageType}_image_s3_url`;\n  if (imageData[s3KeyField]) {\n    console.log('Using S3 key field:', s3KeyField, imageData[s3KeyField]);\n\n    // Properly encode the S3 key for URL path\n    const s3Key = imageData[s3KeyField];\n    const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\n    const url = `/api/pavement/get-s3-image/${encodedKey}`;\n    console.log('Generated proxy URL from S3 key:', url);\n    console.log('Original S3 key:', s3Key);\n    console.log('Encoded S3 key:', encodedKey);\n    return url;\n  }\n\n  // Fall back to GridFS endpoint (legacy images)\n  const gridfsIdField = `${imageType}_image_id`;\n  if (imageData[gridfsIdField]) {\n    console.log('Using GridFS field:', gridfsIdField, imageData[gridfsIdField]);\n    const url = `/api/pavement/get-image/${imageData[gridfsIdField]}`;\n    console.log('Generated GridFS URL:', url);\n    return url;\n  }\n\n  // No image URL available\n  console.log('No image URL available for:', imageType, imageData);\n  return null;\n};\n\n/**\r\n * Enhanced Image Component with comprehensive error handling\r\n * Supports S3 URLs, GridFS fallback, and graceful error handling\r\n */\nconst EnhancedImageDisplay = ({\n  imageData,\n  imageType = 'original',\n  alt,\n  className,\n  style,\n  onError\n}) => {\n  _s();\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\n  const [hasError, setHasError] = useState(false);\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\n  useEffect(() => {\n    // Reset state when imageData changes\n    setHasError(false);\n    setFallbackAttempts(0);\n\n    // Get initial image URL\n    const imageUrl = getImageUrlForDisplay(imageData, imageType);\n\n    // Debug logging\n    console.log('EnhancedImageDisplay Debug:', {\n      imageType,\n      imageData,\n      generatedUrl: imageUrl,\n      s3KeyField: `${imageType}_image_s3_url`,\n      s3KeyValue: imageData === null || imageData === void 0 ? void 0 : imageData[`${imageType}_image_s3_url`],\n      fullUrlField: `${imageType}_image_full_url`,\n      fullUrlValue: imageData === null || imageData === void 0 ? void 0 : imageData[`${imageType}_image_full_url`]\n    });\n    setCurrentImageUrl(imageUrl);\n  }, [imageData, imageType]);\n  const handleImageError = event => {\n    var _event$target, _event$target2, _event$target3, _event$target4, _event$target5;\n    console.error('🚨 Image load error:', {\n      imageType,\n      currentImageUrl,\n      fallbackAttempts,\n      error: event === null || event === void 0 ? void 0 : (_event$target = event.target) === null || _event$target === void 0 ? void 0 : _event$target.error,\n      src: event === null || event === void 0 ? void 0 : (_event$target2 = event.target) === null || _event$target2 === void 0 ? void 0 : _event$target2.src,\n      naturalWidth: event === null || event === void 0 ? void 0 : (_event$target3 = event.target) === null || _event$target3 === void 0 ? void 0 : _event$target3.naturalWidth,\n      naturalHeight: event === null || event === void 0 ? void 0 : (_event$target4 = event.target) === null || _event$target4 === void 0 ? void 0 : _event$target4.naturalHeight,\n      complete: event === null || event === void 0 ? void 0 : (_event$target5 = event.target) === null || _event$target5 === void 0 ? void 0 : _event$target5.complete\n    });\n\n    // Test if the URL is reachable\n    if (currentImageUrl) {\n      fetch(currentImageUrl, {\n        method: 'HEAD'\n      }).then(response => {\n        console.log('🔍 URL HEAD check:', {\n          url: currentImageUrl,\n          status: response.status,\n          statusText: response.statusText,\n          headers: Object.fromEntries(response.headers.entries())\n        });\n      }).catch(fetchError => {\n        console.error('🚨 URL HEAD check failed:', {\n          url: currentImageUrl,\n          error: fetchError.message\n        });\n      });\n    }\n    if (fallbackAttempts === 0) {\n      // First error: try alternative image type or fallback\n      const fallbackUrl = getFallbackImageUrl(imageData, imageType);\n      console.log('🔄 Trying fallback URL:', fallbackUrl);\n      if (fallbackUrl && fallbackUrl !== currentImageUrl) {\n        setCurrentImageUrl(fallbackUrl);\n        setFallbackAttempts(1);\n        return;\n      }\n    }\n\n    // All fallbacks failed\n    console.error('❌ All image loading attempts failed for:', imageType);\n    setHasError(true);\n    if (onError) onError();\n  };\n  const getFallbackImageUrl = (imageData, imageType) => {\n    console.log('🔄 Getting fallback URL for:', imageType, imageData);\n\n    // Try direct S3 URL if we have the full URL field\n    const fullUrlField = `${imageType}_image_full_url`;\n    if (imageData[fullUrlField]) {\n      console.log('🔄 Trying direct S3 URL:', imageData[fullUrlField]);\n      return imageData[fullUrlField];\n    }\n\n    // Try GridFS if S3 failed\n    const gridfsIdField = `${imageType}_image_id`;\n    if (imageData[gridfsIdField]) {\n      console.log('🔄 Trying GridFS URL:', imageData[gridfsIdField]);\n      return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\n    }\n\n    // Try alternative S3 proxy with different encoding\n    const s3KeyField = `${imageType}_image_s3_url`;\n    if (imageData[s3KeyField]) {\n      console.log('🔄 Trying alternative S3 proxy encoding');\n      const s3Key = imageData[s3KeyField];\n      const alternativeUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\n      console.log('🔄 Alternative proxy URL:', alternativeUrl);\n      return alternativeUrl;\n    }\n    console.log('❌ No fallback URL available');\n    return null;\n  };\n  if (hasError || !currentImageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `text-muted d-flex align-items-center justify-content-center ${className}`,\n      style: style,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-image-slash fa-2x mb-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"No image available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"position-relative\",\n    children: /*#__PURE__*/_jsxDEV(\"img\", {\n      src: currentImageUrl,\n      alt: alt,\n      className: className,\n      style: style,\n      onError: handleImageError,\n      loading: \"lazy\",\n      onLoad: () => {\n        console.log('✅ Image loaded successfully:', currentImageUrl);\n        setHasError(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n\n// ImageCard component to isolate state for each image\n_s(EnhancedImageDisplay, \"MkXbuZWNMKn4hp+/e9W7L0IlcVk=\");\n_c = EnhancedImageDisplay;\nconst ImageCard = ({\n  defect,\n  defectType,\n  defectIdKey\n}) => {\n  _s2();\n  const [isOriginal, setIsOriginal] = useState(false);\n\n  // Safety check: return null if defect is not provided\n  if (!defect) {\n    return null;\n  }\n  const toggleView = showOriginal => {\n    setIsOriginal(showOriginal);\n  };\n\n  // Check if this is a multi-defect image\n  const isMultiDefect = defect.detected_defects && defect.detected_defects.length > 1;\n  const detectedDefects = defect.detected_defects || [];\n  return /*#__PURE__*/_jsxDEV(Col, {\n    md: 4,\n    className: \"mb-4\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      className: `h-100 shadow-sm ${isMultiDefect ? 'border-warning' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: isMultiDefect ? 'bg-warning bg-opacity-10' : '',\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-0\",\n            children: defectType === 'cracks' ? `${defect.crack_type || 'Crack'} #${defect.crack_id || 'N/A'}` : defectType === 'kerbs' ? `${defect.condition || 'Kerb'} #${defect.kerb_id || 'N/A'}` : `Pothole #${defect.pothole_id || 'N/A'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), isMultiDefect && /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-warning fw-bold\",\n            children: \"\\uD83D\\uDD00 Multi-Defect\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), isMultiDefect && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1\",\n          children: /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Also contains: \", detectedDefects.filter(d => d !== defectType).join(', ')]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-2 text-center\",\n          children: /*#__PURE__*/_jsxDEV(EnhancedImageDisplay, {\n            imageData: defect,\n            imageType: isOriginal ? 'original' : 'processed',\n            alt: `${defectType === 'cracks' ? 'Crack' : defectType === 'kerbs' ? 'Kerb' : 'Pothole'} ${defect[defectIdKey]}`,\n            className: \"img-fluid mb-2 border\",\n            style: {\n              maxHeight: \"200px\"\n            },\n            onError: () => {\n              console.warn(`Failed to load ${isOriginal ? 'original' : 'processed'} image for ${defectType} ${defect[defectIdKey]}`);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"small\",\n          children: [defectType === 'potholes' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Area:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 37\n              }, this), \" \", defect.area_cm2 ? defect.area_cm2.toFixed(2) : 'N/A', \" cm\\xB2\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Depth:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 37\n              }, this), \" \", defect.depth_cm ? defect.depth_cm.toFixed(2) : 'N/A', \" cm\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Volume:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 37\n              }, this), \" \", defect.volume ? defect.volume.toFixed(2) : 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), defectType === 'cracks' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 37\n              }, this), \" \", defect.crack_type || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Area:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 37\n              }, this), \" \", defect.area_cm2 ? defect.area_cm2.toFixed(2) : 'N/A', \" cm\\xB2\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Range:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 37\n              }, this), \" \", defect.area_range || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), defectType === 'kerbs' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Type:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 37\n              }, this), \" \", defect.kerb_type || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Length:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 37\n              }, this), \" \", defect.length_m ? defect.length_m.toFixed(2) : 'N/A', \" m\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mb-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Condition:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 37\n              }, this), \" \", defect.condition || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Uploaded by:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 33\n            }, this), \" \", defect.username || 'Unknown']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Timestamp:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 33\n            }, this), \" \", defect.timestamp ? new Date(defect.timestamp).toLocaleString('en-IN', {\n              timeZone: 'Asia/Kolkata'\n            }) : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: isOriginal ? 'primary' : 'outline-primary',\n              size: \"sm\",\n              className: \"me-2\",\n              onClick: () => toggleView(true),\n              children: \"Original\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: !isOriginal ? 'success' : 'outline-success',\n              size: \"sm\",\n              onClick: () => toggleView(false),\n              children: \"Processed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this)\n  }, `${defectType}-${defect[defectIdKey] || defect.image_id || Math.random()}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 235,\n    columnNumber: 5\n  }, this);\n};\n\n// VideoCard component for displaying processed videos with representative frames\n_s2(ImageCard, \"JETynDfz7zT31hndeMr5RDIJ8rs=\");\n_c2 = ImageCard;\nconst VideoCard = ({\n  video\n}) => {\n  // Safety check: return null if video is not provided\n  if (!video) {\n    return null;\n  }\n  const handleDownload = async videoType => {\n    try {\n      // Use the MongoDB _id for the download endpoint\n      const videoId = video._id || video.video_id;\n      const downloadUrl = `/api/pavement/get-s3-video/${videoId}/${videoType}`;\n      console.log(`🔄 Starting ${videoType} video download for ID: ${videoId}`);\n\n      // Fetch the video data as a blob to ensure proper binary handling\n      const response = await fetch(downloadUrl, {\n        method: 'GET',\n        headers: {\n          'Accept': 'video/mp4, video/*, */*'\n        }\n      });\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({\n          message: 'Unknown error'\n        }));\n        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      // Get the video data as a blob\n      const videoBlob = await response.blob();\n      console.log(`✅ Downloaded ${videoType} video blob - Size: ${videoBlob.size} bytes, Type: ${videoBlob.type}`);\n\n      // Create a blob URL and trigger download\n      const blobUrl = URL.createObjectURL(videoBlob);\n\n      // Generate filename with proper extension\n      const filename = `${videoType}_video_${(video.video_id || videoId).substring(0, 8)}.mp4`;\n\n      // Create and trigger download link\n      const link = document.createElement('a');\n      link.href = blobUrl;\n      link.download = filename;\n      link.style.display = 'none';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n      // Clean up the blob URL after a short delay to ensure download starts\n      setTimeout(() => {\n        URL.revokeObjectURL(blobUrl);\n        console.log(`🧹 Cleaned up blob URL for ${videoType} video`);\n      }, 1000);\n    } catch (error) {\n      console.error(`❌ Error downloading ${videoType} video:`, error);\n      alert(`Error downloading ${videoType} video: ${error.message}`);\n    }\n  };\n  const handleExport = async format => {\n    try {\n      const exportFormat = format.toLowerCase();\n      const videoId = video._id || video.video_id;\n\n      // Call backend API for export with detailed detection tables\n      const response = await fetch(`/api/dashboard/video-processing-export?format=${exportFormat}&video_id=${videoId}`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`Export failed: ${response.statusText}`);\n      }\n      const data = await response.json();\n      if (exportFormat === 'pdf') {\n        // Handle PDF download\n        if (data.pdf_data) {\n          const byteCharacters = atob(data.pdf_data);\n          const byteNumbers = new Array(byteCharacters.length);\n          for (let i = 0; i < byteCharacters.length; i++) {\n            byteNumbers[i] = byteCharacters.charCodeAt(i);\n          }\n          const byteArray = new Uint8Array(byteNumbers);\n          const blob = new Blob([byteArray], {\n            type: 'application/pdf'\n          });\n          const link = document.createElement('a');\n          const url = URL.createObjectURL(blob);\n          link.setAttribute('href', url);\n          link.setAttribute('download', `video_${(video.video_id || videoId).substring(0, 8)}_detailed_report.pdf`);\n          link.style.visibility = 'hidden';\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          URL.revokeObjectURL(url);\n        }\n      } else if (exportFormat === 'csv') {\n        // Handle CSV download\n        if (data.csv_data) {\n          const csvContent = data.csv_data.map(row => row.join(',')).join('\\n');\n          const blob = new Blob([csvContent], {\n            type: 'text/csv;charset=utf-8;'\n          });\n          const link = document.createElement('a');\n          const url = URL.createObjectURL(blob);\n          link.setAttribute('href', url);\n          link.setAttribute('download', `video_${(video.video_id || videoId).substring(0, 8)}_detailed_report.csv`);\n          link.style.visibility = 'hidden';\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          URL.revokeObjectURL(url);\n        }\n      }\n    } catch (error) {\n      console.error(`Error exporting to ${format}:`, error);\n      alert(`Error exporting to ${format}: ${error.message}`);\n    }\n  };\n  const detectionCounts = video.detection_counts || {};\n  const totalDetections = detectionCounts.total || 0;\n  return /*#__PURE__*/_jsxDEV(Col, {\n    md: 4,\n    className: \"mb-4\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      className: \"h-100 shadow-sm\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"bg-info bg-opacity-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"mb-0\",\n            children: [\"Video #\", video.video_id.substring(0, 8), \"...\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-info fw-bold\",\n            children: \"\\uD83D\\uDCF9 Video\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-1\",\n          children: /*#__PURE__*/_jsxDEV(\"small\", {\n            className: \"text-muted\",\n            children: [\"Models: \", video.models_run ? video.models_run.join(', ') : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 447,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-2 text-center\",\n          children: video.representative_frame ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/jpeg;base64,${video.representative_frame}`,\n            alt: \"Video thumbnail\",\n            className: \"img-fluid mb-2 border\",\n            style: {\n              maxHeight: \"200px\"\n            },\n            onError: e => {\n              console.warn(`Failed to load representative frame for video ${video.video_id}`);\n              e.target.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center justify-content-center border\",\n            style: {\n              height: \"200px\",\n              backgroundColor: \"#f8f9fa\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-muted\",\n              children: \"No thumbnail available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: /*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Detections:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mb-2\",\n            style: {\n              paddingLeft: '20px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"Potholes: \", detectionCounts.potholes || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"Cracks: \", detectionCounts.cracks || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [\"Kerbs: \", detectionCounts.kerbs || 0]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Total Detections:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 33\n            }, this), \" \", totalDetections]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Uploaded by:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 33\n            }, this), \" \", video.username || 'Unknown']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Timestamp:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 33\n            }, this), \" \", video.timestamp ? new Date(video.timestamp).toLocaleString('en-IN', {\n              timeZone: 'Asia/Kolkata'\n            }) : 'N/A']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              size: \"sm\",\n              className: \"me-2 mb-1\",\n              onClick: () => handleDownload('original'),\n              disabled: !video.original_video_url,\n              children: \"\\uD83D\\uDCE5 Original Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"success\",\n              size: \"sm\",\n              className: \"me-2 mb-1\",\n              onClick: () => handleDownload('processed'),\n              disabled: !video.processed_video_url,\n              children: \"\\uD83D\\uDCE5 Processed Video\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              className: \"me-2\",\n              onClick: () => handleExport('PDF'),\n              children: \"\\uD83D\\uDCC4 Export PDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-secondary\",\n              size: \"sm\",\n              onClick: () => handleExport('CSV'),\n              children: \"\\uD83D\\uDCCA Export CSV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 446,\n      columnNumber: 7\n    }, this)\n  }, `video-${video.video_id}`, false, {\n    fileName: _jsxFileName,\n    lineNumber: 445,\n    columnNumber: 5\n  }, this);\n};\n_c3 = VideoCard;\nfunction Dashboard({\n  user\n}) {\n  _s3();\n  var _dashboardData$videos, _dashboardData$videos2, _dashboardData$videos3, _dashboardData$users2, _dashboardData$users3, _dashboardData$users4;\n  const [statistics, setStatistics] = useState({\n    potholesDetected: 0,\n    cracksDetected: 0,\n    kerbsDetected: 0,\n    totalUsers: 0\n  });\n  const [weeklyData, setWeeklyData] = useState({\n    days: [],\n    issues: []\n  });\n  const [issuesByType, setIssuesByType] = useState({\n    types: [],\n    counts: []\n  });\n  const [dashboardData, setDashboardData] = useState({\n    potholes: {\n      count: 0,\n      by_size: {},\n      avg_volume: 0,\n      latest: []\n    },\n    cracks: {\n      count: 0,\n      by_type: {},\n      by_size: {},\n      latest: []\n    },\n    kerbs: {\n      count: 0,\n      by_condition: {},\n      latest: []\n    },\n    users: {\n      count: 0,\n      by_role: {},\n      latest: []\n    },\n    videos: {\n      count: 0,\n      latest: []\n    }\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Date filter state\n  const [startDate, setStartDate] = useState('');\n  const [endDate, setEndDate] = useState('');\n  const [dateFilterApplied, setDateFilterApplied] = useState(false);\n\n  // User filter state\n  const [usersList, setUsersList] = useState([]);\n  const [selectedUser, setSelectedUser] = useState('');\n  const [userFilterApplied, setUserFilterApplied] = useState(false);\n\n  // Defect type filter state\n  const [defectFilters, setDefectFilters] = useState({\n    potholes: true,\n    cracks: true,\n    kerbs: true\n  });\n\n  // Filtered issues state\n  const [filteredIssuesByType, setFilteredIssuesByType] = useState({\n    types: [],\n    counts: []\n  });\n\n  // Dashboard tab state\n  const [activeTab, setActiveTab] = useState('dashboard');\n\n  // Set default date range to previous week and auto-apply filter\n  useEffect(() => {\n    const currentDate = new Date();\n    const lastWeek = new Date();\n    lastWeek.setDate(currentDate.getDate() - 6); // 6 days ago + today = 7 days\n\n    const formattedEndDate = currentDate.toISOString().split('T')[0];\n    const formattedStartDate = lastWeek.toISOString().split('T')[0];\n    setEndDate(formattedEndDate);\n    setStartDate(formattedStartDate);\n    fetchData({\n      startDate: formattedStartDate,\n      endDate: formattedEndDate\n    });\n  }, []);\n\n  // Fetch dashboard data from backend\n  const fetchData = async (filters = {}) => {\n    try {\n      setLoading(true);\n\n      // Add filters to requests if provided\n      const params = {};\n      if (filters.startDate) params.start_date = filters.startDate;\n      if (filters.endDate) params.end_date = filters.endDate;\n      if (filters.username) params.username = filters.username;\n      if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n\n      // Get overview statistics\n      const statsResponse = await axios.get('/api/dashboard/statistics', {\n        params\n      });\n      if (statsResponse.data.success) {\n        setStatistics({\n          potholesDetected: statsResponse.data.data.issues_by_type.potholes,\n          cracksDetected: statsResponse.data.data.issues_by_type.cracks,\n          kerbsDetected: statsResponse.data.data.issues_by_type.kerbs,\n          totalUsers: statistics.totalUsers // Preserve the existing user count\n        });\n      }\n\n      // Get weekly trend data\n      const weeklyResponse = await axios.get('/api/dashboard/weekly-trend', {\n        params\n      });\n      setWeeklyData({\n        days: weeklyResponse.data.days,\n        issues: weeklyResponse.data.issues\n      });\n\n      // Get issues by type\n      const typesResponse = await axios.get('/api/dashboard/issues-by-type', {\n        params\n      });\n      setIssuesByType({\n        types: typesResponse.data.types,\n        counts: typesResponse.data.counts\n      });\n\n      // Get detailed dashboard data including latest images with enhanced S3-MongoDB integration\n      let dashboardResponse;\n      try {\n        // Try enhanced API endpoint first (with comprehensive S3-MongoDB integration)\n        dashboardResponse = await axios.get('/api/dashboard/summary-v2', {\n          params\n        });\n        console.log('✅ Using enhanced dashboard API with comprehensive S3-MongoDB integration');\n      } catch (enhancedError) {\n        console.warn('⚠️ Enhanced API not available, falling back to standard API:', enhancedError.message);\n        // Fallback to original API\n        dashboardResponse = await axios.get('/api/dashboard/summary', {\n          params\n        });\n        console.log('✅ Using standard dashboard API');\n      }\n      if (dashboardResponse.data.success) {\n        const dashboardData = dashboardResponse.data.data;\n\n        // Calculate multi-defect statistics\n        const multiDefectStats = {\n          totalImages: 0,\n          multiDefectImages: 0,\n          singleDefectImages: 0,\n          categoryBreakdown: {\n            potholes: 0,\n            cracks: 0,\n            kerbs: 0\n          }\n        };\n\n        // Count multi-defect images from each category\n        ['potholes', 'cracks', 'kerbs'].forEach(category => {\n          const categoryImages = dashboardData[category].latest || [];\n          categoryImages.forEach(item => {\n            if (item.multi_defect_image) {\n              multiDefectStats.multiDefectImages++;\n            }\n            multiDefectStats.totalImages++;\n            multiDefectStats.categoryBreakdown[category]++;\n          });\n        });\n        multiDefectStats.singleDefectImages = multiDefectStats.totalImages - multiDefectStats.multiDefectImages;\n\n        // Add multi-defect statistics to dashboard data\n        dashboardData.multiDefectStats = multiDefectStats;\n        setDashboardData(dashboardData);\n      }\n\n      // Get users data\n      try {\n        const usersResponse = await axios.get('/api/users/summary', {\n          params\n        });\n        if (usersResponse.data.success) {\n          setStatistics(prevStats => ({\n            ...prevStats,\n            totalUsers: usersResponse.data.total_users || 0\n          }));\n\n          // Ensure users data is properly set in dashboardData\n          setDashboardData(prevData => ({\n            ...prevData,\n            users: {\n              count: usersResponse.data.total_users || 0,\n              by_role: usersResponse.data.roles_distribution || {},\n              latest: usersResponse.data.recent_users || []\n            }\n          }));\n        }\n      } catch (userErr) {\n        console.error('Error fetching user data:', userErr);\n        // Non-critical error, continue with other data\n      }\n      setLoading(false);\n    } catch (err) {\n      setError('Error fetching dashboard data');\n      setLoading(false);\n      console.error('Error fetching data:', err);\n    }\n  };\n\n  // Fetch users list for filter dropdown\n  useEffect(() => {\n    const fetchUsers = async () => {\n      try {\n        const params = {};\n        if (user !== null && user !== void 0 && user.role) params.user_role = user.role;\n        const response = await axios.get('/api/users/all', {\n          params\n        });\n        if (response.data.success) {\n          // Users are already filtered by the backend based on RBAC\n          setUsersList(response.data.users);\n        }\n      } catch (error) {\n        console.error('Error fetching users:', error);\n      }\n    };\n    fetchUsers();\n  }, [user]);\n\n  // Handle date filter application\n  const handleApplyDateFilter = () => {\n    if (startDate && endDate) {\n      fetchData({\n        startDate,\n        endDate,\n        username: selectedUser || undefined\n      });\n      setDateFilterApplied(true);\n    }\n  };\n\n  // Handle date filter reset\n  const handleResetDateFilter = () => {\n    const currentDate = new Date();\n    const lastWeek = new Date();\n    lastWeek.setDate(currentDate.getDate() - 6); // 6 days ago + today = 7 days\n\n    const newEndDate = currentDate.toISOString().split('T')[0];\n    const newStartDate = lastWeek.toISOString().split('T')[0];\n    setEndDate(newEndDate);\n    setStartDate(newStartDate);\n    fetchData({\n      startDate: newStartDate,\n      endDate: newEndDate,\n      username: selectedUser || undefined\n    });\n    setDateFilterApplied(false);\n  };\n\n  // Handle user filter application\n  const handleApplyUserFilter = () => {\n    fetchData({\n      startDate,\n      endDate,\n      username: selectedUser || undefined\n    });\n    setUserFilterApplied(!!selectedUser);\n  };\n\n  // Handle user filter reset\n  const handleResetUserFilter = () => {\n    setSelectedUser('');\n    fetchData({\n      startDate,\n      endDate\n    });\n    setUserFilterApplied(false);\n  };\n\n  // Handle user selection\n  const handleUserChange = e => {\n    setSelectedUser(e.target.value);\n  };\n\n  // Filter the issues by type whenever the filters or data changes\n  useEffect(() => {\n    if (issuesByType.types.length > 0) {\n      const filteredTypes = [];\n      const filteredCounts = [];\n      issuesByType.types.forEach((type, index) => {\n        if (type.includes('Pothole') && defectFilters.potholes || type.includes('Crack') && defectFilters.cracks || type.includes('Kerb') && defectFilters.kerbs) {\n          filteredTypes.push(type);\n          filteredCounts.push(issuesByType.counts[index]);\n        }\n      });\n      setFilteredIssuesByType({\n        types: filteredTypes,\n        counts: filteredCounts\n      });\n    }\n  }, [issuesByType, defectFilters]);\n\n  // Handle defect filter change\n  const handleDefectFilterChange = defectType => {\n    setDefectFilters(prev => ({\n      ...prev,\n      [defectType]: !prev[defectType]\n    }));\n  };\n\n  // Add export handlers\n  const handleDownloadPDF = () => {\n    var _dashboardData$users;\n    const doc = new jsPDF();\n    let yPosition = 20;\n\n    // Header\n    doc.setFontSize(20);\n    doc.setFont('helvetica', 'bold');\n    doc.text('Road AI Safety Enhancement - Dashboard Report', 105, yPosition, {\n      align: 'center'\n    });\n    yPosition += 15;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Generated on: ${new Date().toLocaleString('en-IN', {\n      timeZone: 'Asia/Kolkata'\n    })}`, 105, yPosition, {\n      align: 'center'\n    });\n    yPosition += 20;\n\n    // Date Range Info\n    if (dateFilterApplied) {\n      doc.setFontSize(14);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Date Range Filter:', 14, yPosition);\n      yPosition += 8;\n      doc.setFontSize(12);\n      doc.setFont('helvetica', 'normal');\n      doc.text(`From: ${new Date(startDate).toLocaleDateString()} To: ${new Date(endDate).toLocaleDateString()}`, 14, yPosition);\n      yPosition += 15;\n    }\n\n    // Statistics Summary\n    doc.setFontSize(16);\n    doc.setFont('helvetica', 'bold');\n    doc.text('Statistics Summary', 14, yPosition);\n    yPosition += 10;\n    doc.setFontSize(12);\n    doc.setFont('helvetica', 'normal');\n    doc.text(`Total Potholes Detected: ${statistics.potholesDetected}`, 14, yPosition);\n    yPosition += 7;\n    doc.text(`Total Cracks Detected: ${statistics.cracksDetected}`, 14, yPosition);\n    yPosition += 7;\n    doc.text(`Total Kerbs Detected: ${statistics.kerbsDetected}`, 14, yPosition);\n    yPosition += 7;\n    doc.text(`Total Users: ${statistics.totalUsers}`, 14, yPosition);\n    yPosition += 15;\n\n    // Infrastructure Distribution\n    doc.setFontSize(16);\n    doc.setFont('helvetica', 'bold');\n    doc.text('Infrastructure Distribution', 14, yPosition);\n    yPosition += 10;\n    const totalIssues = statistics.potholesDetected + statistics.cracksDetected + statistics.kerbsDetected;\n    if (totalIssues > 0) {\n      const potholePercent = (statistics.potholesDetected / totalIssues * 100).toFixed(1);\n      const crackPercent = (statistics.cracksDetected / totalIssues * 100).toFixed(1);\n      const kerbPercent = (statistics.kerbsDetected / totalIssues * 100).toFixed(1);\n      doc.setFontSize(12);\n      doc.setFont('helvetica', 'normal');\n      doc.text(`Potholes: ${statistics.potholesDetected} (${potholePercent}%)`, 14, yPosition);\n      yPosition += 7;\n      doc.text(`Cracks: ${statistics.cracksDetected} (${crackPercent}%)`, 14, yPosition);\n      yPosition += 7;\n      doc.text(`Kerbs: ${statistics.kerbsDetected} (${kerbPercent}%)`, 14, yPosition);\n      yPosition += 15;\n    }\n\n    // User Overview\n    if ((_dashboardData$users = dashboardData.users) !== null && _dashboardData$users !== void 0 && _dashboardData$users.latest && dashboardData.users.latest.length > 0) {\n      doc.setFontSize(16);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Recent Users', 14, yPosition);\n      yPosition += 10;\n      const userTableData = dashboardData.users.latest.map((user, idx) => [idx + 1, user.username, user.role, new Date(user.last_login).toLocaleString('en-IN', {\n        timeZone: 'Asia/Kolkata'\n      })]);\n      autoTable(doc, {\n        head: [['#', 'Username', 'Role', 'Last Login']],\n        body: userTableData,\n        startY: yPosition,\n        margin: {\n          top: 10\n        },\n        styles: {\n          fontSize: 10\n        },\n        headStyles: {\n          fillColor: [0, 123, 255]\n        }\n      });\n      yPosition = doc.lastAutoTable.finalY + 15;\n    }\n\n    // Potholes Section\n    if (dashboardData.potholes.latest && dashboardData.potholes.latest.length > 0) {\n      doc.setFontSize(16);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Potholes Detected', 14, yPosition);\n      yPosition += 10;\n      const potholeTableData = dashboardData.potholes.latest.map((defect, idx) => [idx + 1, defect.area_cm2 ? defect.area_cm2.toFixed(2) + ' cm²' : 'N/A', defect.depth_cm ? defect.depth_cm.toFixed(2) + ' cm' : 'N/A', defect.volume ? defect.volume.toFixed(2) : 'N/A', defect.username || 'Unknown', new Date(defect.timestamp).toLocaleString('en-IN', {\n        timeZone: 'Asia/Kolkata'\n      })]);\n      autoTable(doc, {\n        head: [['#', 'Area', 'Depth', 'Volume', 'Uploaded By', 'Timestamp']],\n        body: potholeTableData,\n        startY: yPosition,\n        margin: {\n          top: 10\n        },\n        styles: {\n          fontSize: 9\n        },\n        headStyles: {\n          fillColor: [220, 53, 69]\n        }\n      });\n      yPosition = doc.lastAutoTable.finalY + 15;\n    }\n\n    // Cracks Section\n    if (dashboardData.cracks.latest && dashboardData.cracks.latest.length > 0) {\n      doc.setFontSize(16);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Cracks Detected', 14, yPosition);\n      yPosition += 10;\n      const crackTableData = dashboardData.cracks.latest.map((defect, idx) => [idx + 1, defect.crack_type || 'Unknown', defect.area_cm2 ? defect.area_cm2.toFixed(2) + ' cm²' : 'N/A', defect.area_range || 'N/A', defect.username || 'Unknown', new Date(defect.timestamp).toLocaleString('en-IN', {\n        timeZone: 'Asia/Kolkata'\n      })]);\n      autoTable(doc, {\n        head: [['#', 'Type', 'Area', 'Range', 'Uploaded By', 'Timestamp']],\n        body: crackTableData,\n        startY: yPosition,\n        margin: {\n          top: 10\n        },\n        styles: {\n          fontSize: 9\n        },\n        headStyles: {\n          fillColor: [40, 167, 69]\n        }\n      });\n      yPosition = doc.lastAutoTable.finalY + 15;\n    }\n\n    // Kerbs Section\n    if (dashboardData.kerbs.latest && dashboardData.kerbs.latest.length > 0) {\n      doc.setFontSize(16);\n      doc.setFont('helvetica', 'bold');\n      doc.text('Kerbs Detected', 14, yPosition);\n      yPosition += 10;\n      const kerbTableData = dashboardData.kerbs.latest.map((defect, idx) => [idx + 1, defect.kerb_type || 'Unknown', defect.length_m ? defect.length_m.toFixed(2) + ' m' : 'N/A', defect.condition || 'Unknown', defect.username || 'Unknown', new Date(defect.timestamp).toLocaleString('en-IN', {\n        timeZone: 'Asia/Kolkata'\n      })]);\n      autoTable(doc, {\n        head: [['#', 'Type', 'Length', 'Condition', 'Uploaded By', 'Timestamp']],\n        body: kerbTableData,\n        startY: yPosition,\n        margin: {\n          top: 10\n        },\n        styles: {\n          fontSize: 9\n        },\n        headStyles: {\n          fillColor: [0, 123, 255]\n        }\n      });\n    }\n\n    // Footer\n    const pageCount = doc.internal.getNumberOfPages();\n    for (let i = 1; i <= pageCount; i++) {\n      doc.setPage(i);\n      doc.setFontSize(10);\n      doc.setFont('helvetica', 'italic');\n      doc.text(`Page ${i} of ${pageCount}`, 105, doc.internal.pageSize.height - 10, {\n        align: 'center'\n      });\n    }\n    doc.save('Dashboard_Report.pdf');\n  };\n  const handleDownloadExcel = () => {\n    const wsData = [['#', 'Area (cm²)', 'Depth (cm)', 'Volume', 'Uploaded By', 'Timestamp'], ...(dashboardData.potholes.latest || []).map((defect, idx) => [idx + 1, defect.area, defect.depth, defect.volume, defect.username, new Date(defect.timestamp).toLocaleString('en-IN', {\n      timeZone: 'Asia/Kolkata'\n    })])];\n    const ws = XLSX.utils.aoa_to_sheet(wsData);\n    const wb = XLSX.utils.book_new();\n    XLSX.utils.book_append_sheet(wb, ws, 'Processed Report');\n    const wbout = XLSX.write(wb, {\n      bookType: 'xlsx',\n      type: 'array'\n    });\n    saveAs(new Blob([wbout], {\n      type: 'application/octet-stream'\n    }), 'Processed_Report.xlsx');\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      className: \"mb-3 shadow-sm dashboard-card filters-card\",\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"bg-primary text-white py-2\",\n        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n          className: \"mb-0\",\n          children: \"Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1061,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1060,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        className: \"py-3\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"g-3\",\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-2\",\n                children: \"Date Range\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1067,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filter-field\",\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"small mb-1\",\n                      children: \"Start Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1071,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"date\",\n                      value: startDate,\n                      onChange: e => setStartDate(e.target.value),\n                      size: \"sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1072,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filter-field\",\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"small mb-1\",\n                      children: \"End Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1082,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"date\",\n                      value: endDate,\n                      onChange: e => setEndDate(e.target.value),\n                      size: \"sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1083,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1081,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filter-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"primary\",\n                    onClick: handleApplyDateFilter,\n                    disabled: !startDate || !endDate,\n                    children: \"Apply\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1092,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-secondary\",\n                    onClick: handleResetDateFilter,\n                    disabled: !dateFilterApplied,\n                    children: \"Reset\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1100,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1091,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 17\n              }, this), dateFilterApplied && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-status text-success mt-2 p-2\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Showing data from \", new Date(startDate).toLocaleDateString(), \" to \", new Date(endDate).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1112,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1111,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1066,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1065,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            lg: 6,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"filter-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                className: \"mb-2\",\n                children: \"User Filter\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-controls\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filter-field\",\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"small mb-1\",\n                      children: \"Select User\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1123,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      value: selectedUser,\n                      onChange: handleUserChange,\n                      size: \"sm\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"All Users\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1129,\n                        columnNumber: 25\n                      }, this), usersList.map((user, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: user.username,\n                        children: [user.username, \" (\", user.role, \")\"]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1131,\n                        columnNumber: 27\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1124,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1122,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1121,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"filter-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"primary\",\n                    onClick: handleApplyUserFilter,\n                    children: \"Apply\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"sm\",\n                    variant: \"outline-secondary\",\n                    onClick: handleResetUserFilter,\n                    disabled: !userFilterApplied,\n                    children: \"Reset\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1146,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1120,\n                columnNumber: 17\n              }, this), userFilterApplied && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"filter-status text-success mt-2 p-2\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Showing data for user: \", selectedUser]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1158,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1157,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1064,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1063,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1059,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1170,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1169,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1168,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"alert alert-danger p-3\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1174,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        className: \"mb-3 shadow-sm dashboard-card\",\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          className: \"p-0\",\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            activeKey: activeTab,\n            onSelect: k => setActiveTab(k),\n            className: \"dashboard-tabs\",\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              eventKey: \"dashboard\",\n              title: \"Dashboard View\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(Row, {\n                  className: \"mb-3 g-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 3,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"h-100 shadow-sm dashboard-card stats-card\",\n                      children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"text-center py-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"card-title mb-2\",\n                          children: \"Potholes\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1193,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-primary mb-0\",\n                          children: statistics.potholesDetected\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1194,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1192,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1191,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1190,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 3,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"h-100 shadow-sm dashboard-card stats-card\",\n                      children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"text-center py-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"card-title mb-2\",\n                          children: \"Cracks\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1201,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-primary mb-0\",\n                          children: statistics.cracksDetected\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1202,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1200,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1199,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1198,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 3,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"h-100 shadow-sm dashboard-card stats-card\",\n                      children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"text-center py-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"card-title mb-2\",\n                          children: \"Kerbs\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1209,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-primary mb-0\",\n                          children: statistics.kerbsDetected\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1210,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1208,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1207,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1206,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 3,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"h-100 shadow-sm dashboard-card stats-card\",\n                      children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"text-center py-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"card-title mb-2\",\n                          children: \"Users\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1217,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-success mb-0\",\n                          children: statistics.totalUsers\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1218,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1216,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1215,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1214,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  className: \"mb-3 g-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"shadow-sm dashboard-card\",\n                      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                        className: \"bg-primary text-white py-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: \"Weekly Detection Trend\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1229,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1228,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"py-3\",\n                        children: /*#__PURE__*/_jsxDEV(ChartContainer, {\n                          data: [{\n                            x: weeklyData.days,\n                            y: weeklyData.issues,\n                            type: 'scatter',\n                            mode: 'lines+markers',\n                            marker: {\n                              color: '#007bff'\n                            }\n                          }],\n                          layout: {\n                            xaxis: {\n                              title: 'Day'\n                            },\n                            yaxis: {\n                              title: 'Issues Detected'\n                            }\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1232,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1231,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1227,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1226,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"shadow-sm dashboard-card\",\n                      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                        className: \"bg-primary text-white py-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: \"Issues by Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1254,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1253,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"py-3\",\n                        children: /*#__PURE__*/_jsxDEV(ChartContainer, {\n                          data: [{\n                            type: 'bar',\n                            x: filteredIssuesByType.types,\n                            y: filteredIssuesByType.counts,\n                            marker: {\n                              color: filteredIssuesByType.types.map(type => {\n                                if (type.includes('Pothole')) return '#007bff';\n                                if (type.includes('Crack')) return '#28a745';\n                                if (type.includes('Kerb')) return '#dc3545';\n                                return '#6c757d';\n                              })\n                            }\n                          }],\n                          layout: {\n                            xaxis: {\n                              title: 'Issue Type',\n                              tickangle: -45,\n                              automargin: true\n                            },\n                            yaxis: {\n                              title: 'Count'\n                            },\n                            margin: {\n                              t: 10,\n                              b: 80,\n                              l: 50,\n                              r: 10\n                            }\n                          },\n                          showLegend: true,\n                          legendItems: [{\n                            label: 'Potholes',\n                            color: '#007bff',\n                            checked: defectFilters.potholes,\n                            onChange: () => handleDefectFilterChange('potholes')\n                          }, {\n                            label: 'Cracks',\n                            color: '#28a745',\n                            checked: defectFilters.cracks,\n                            onChange: () => handleDefectFilterChange('cracks')\n                          }, {\n                            label: 'Kerbs',\n                            color: '#dc3545',\n                            checked: defectFilters.kerbs,\n                            onChange: () => handleDefectFilterChange('kerbs')\n                          }],\n                          className: \"compact-legend\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1257,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1256,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1252,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1251,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1225,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Col, {\n                    md: 12,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"shadow-sm dashboard-card\",\n                      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                        className: \"bg-primary text-white py-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: \"Infrastructure Distribution\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1315,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1314,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"py-3\",\n                        children: /*#__PURE__*/_jsxDEV(ChartContainer, {\n                          data: [{\n                            type: 'pie',\n                            labels: ['Potholes', 'Cracks', 'Kerbs'],\n                            values: [statistics.potholesDetected, statistics.cracksDetected, statistics.kerbsDetected],\n                            marker: {\n                              colors: ['#007bff', '#28a745', '#dc3545']\n                            },\n                            textinfo: \"label+percent\",\n                            insidetextorientation: \"radial\"\n                          }],\n                          layout: {\n                            height: 300\n                          },\n                          isPieChart: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1318,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1317,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1313,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1312,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Col, {\n                    md: 12,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"shadow-sm dashboard-card\",\n                      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                        className: \"bg-primary text-white py-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: \"All Uploaded Images\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1350,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1349,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"py-3\",\n                        children: /*#__PURE__*/_jsxDEV(Tabs, {\n                          defaultActiveKey: \"potholes\",\n                          className: \"mb-2\",\n                          children: [/*#__PURE__*/_jsxDEV(Tab, {\n                            eventKey: \"potholes\",\n                            title: `Potholes (${dashboardData.potholes.latest.length})`,\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                maxHeight: '700px',\n                                overflowY: 'auto',\n                                paddingRight: '10px'\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Row, {\n                                children: dashboardData.potholes.latest.map((pothole, index) => /*#__PURE__*/_jsxDEV(ImageCard, {\n                                  defect: pothole,\n                                  defectType: \"potholes\",\n                                  defectIdKey: \"pothole_id\"\n                                }, `pothole-${pothole.pothole_id || pothole.image_id || index}`, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1358,\n                                  columnNumber: 39\n                                }, this))\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1356,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1355,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1354,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                            eventKey: \"cracks\",\n                            title: `Cracks (${dashboardData.cracks.latest.length})`,\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                maxHeight: '700px',\n                                overflowY: 'auto',\n                                paddingRight: '10px'\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Row, {\n                                children: dashboardData.cracks.latest.map((crack, index) => /*#__PURE__*/_jsxDEV(ImageCard, {\n                                  defect: crack,\n                                  defectType: \"cracks\",\n                                  defectIdKey: \"crack_id\"\n                                }, `crack-${crack.crack_id || crack.image_id || index}`, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1372,\n                                  columnNumber: 39\n                                }, this))\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1370,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1369,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1368,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                            eventKey: \"kerbs\",\n                            title: `Kerbs (${dashboardData.kerbs.latest.length})`,\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                maxHeight: '700px',\n                                overflowY: 'auto',\n                                paddingRight: '10px'\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Row, {\n                                children: dashboardData.kerbs && dashboardData.kerbs.latest && dashboardData.kerbs.latest.length > 0 ? dashboardData.kerbs.latest.map((kerb, index) => /*#__PURE__*/_jsxDEV(ImageCard, {\n                                  defect: kerb,\n                                  defectType: \"kerbs\",\n                                  defectIdKey: \"kerb_id\"\n                                }, `kerb-${kerb.kerb_id || kerb.image_id || index}`, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1387,\n                                  columnNumber: 41\n                                }, this)) : /*#__PURE__*/_jsxDEV(Col, {\n                                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"alert alert-info p-3\",\n                                    children: \"No kerb images available yet. Upload some kerb images using the Pavement Analysis tool.\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1396,\n                                    columnNumber: 41\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1395,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1384,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1383,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1382,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1353,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1352,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1348,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1347,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Col, {\n                    md: 12,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"shadow-sm dashboard-card\",\n                      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                        className: \"bg-info text-white py-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: [\"All Videos Processed (\", ((_dashboardData$videos = dashboardData.videos) === null || _dashboardData$videos === void 0 ? void 0 : (_dashboardData$videos2 = _dashboardData$videos.latest) === null || _dashboardData$videos2 === void 0 ? void 0 : _dashboardData$videos2.length) || 0, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1415,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1414,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"py-3\",\n                        children: (_dashboardData$videos3 = dashboardData.videos) !== null && _dashboardData$videos3 !== void 0 && _dashboardData$videos3.latest && dashboardData.videos.latest.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            maxHeight: '700px',\n                            overflowY: 'auto',\n                            paddingRight: '10px'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(Row, {\n                            children: dashboardData.videos.latest.map((video, index) => /*#__PURE__*/_jsxDEV(VideoCard, {\n                              video: video\n                            }, `video-${video.video_id || index}`, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1422,\n                              columnNumber: 37\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1420,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1419,\n                          columnNumber: 31\n                        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"alert alert-info p-3\",\n                          children: \"No processed videos available yet. Upload and process some videos using the Video Defect Detection tool.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1430,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1417,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1413,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1412,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1187,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1186,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              eventKey: \"map\",\n              title: \"Defect Map View\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/_jsxDEV(DefectMap, {\n                  user: user\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1444,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1443,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              eventKey: \"users\",\n              title: \"Users Overview\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-3\",\n                children: /*#__PURE__*/_jsxDEV(Row, {\n                  className: \"g-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"shadow-sm dashboard-card\",\n                      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                        className: \"bg-primary text-white py-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: \"User Distribution by Role\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1455,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1454,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"py-3\",\n                        children: /*#__PURE__*/_jsxDEV(ChartContainer, {\n                          data: [{\n                            type: 'pie',\n                            labels: Object.keys(((_dashboardData$users2 = dashboardData.users) === null || _dashboardData$users2 === void 0 ? void 0 : _dashboardData$users2.by_role) || {}),\n                            values: Object.values(((_dashboardData$users3 = dashboardData.users) === null || _dashboardData$users3 === void 0 ? void 0 : _dashboardData$users3.by_role) || {}),\n                            marker: {\n                              colors: ['#007bff', '#28a745', '#dc3545', '#6c757d']\n                            },\n                            textinfo: \"label+percent\",\n                            insidetextorientation: \"radial\"\n                          }],\n                          isPieChart: true\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1458,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1457,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1453,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1452,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    md: 6,\n                    children: /*#__PURE__*/_jsxDEV(Card, {\n                      className: \"shadow-sm dashboard-card\",\n                      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                        className: \"bg-primary text-white py-2\",\n                        children: /*#__PURE__*/_jsxDEV(\"h6\", {\n                          className: \"mb-0\",\n                          children: \"Recent Users\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1479,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1478,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                        className: \"py-3\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"table-responsive\",\n                          children: /*#__PURE__*/_jsxDEV(\"table\", {\n                            className: \"table table-sm table-hover\",\n                            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                                  children: \"Username\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1486,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                                  children: \"Role\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1487,\n                                  columnNumber: 37\n                                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                                  children: \"Last Login\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1488,\n                                  columnNumber: 37\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1485,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1484,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                              children: (_dashboardData$users4 = dashboardData.users) !== null && _dashboardData$users4 !== void 0 && _dashboardData$users4.latest && dashboardData.users.latest.length > 0 ? dashboardData.users.latest.map((user, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                                  children: user.username\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1495,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: `badge bg-${user.role === 'admin' ? 'danger' : user.role === 'manager' ? 'warning' : 'primary'}`,\n                                    children: user.role\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 1497,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1496,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                                  children: new Date(user.last_login).toLocaleString('en-IN', {\n                                    timeZone: 'Asia/Kolkata'\n                                  })\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1505,\n                                  columnNumber: 41\n                                }, this)]\n                              }, `user-${index}`, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1494,\n                                columnNumber: 39\n                              }, this)) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                                  colSpan: \"3\",\n                                  className: \"text-center\",\n                                  children: \"No recent user activity\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1510,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1509,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1491,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1483,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1482,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1481,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1477,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1476,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1451,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1450,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1449,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1180,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1179,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-end mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-danger\",\n          size: \"sm\",\n          className: \"me-2\",\n          onClick: handleDownloadPDF,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-file-pdf me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1529,\n            columnNumber: 15\n          }, this), \"Download PDF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1528,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-success\",\n          size: \"sm\",\n          onClick: handleDownloadExcel,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-file-excel me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1532,\n            columnNumber: 15\n          }, this), \"Download Excel\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1531,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1527,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1057,\n    columnNumber: 5\n  }, this);\n}\n_s3(Dashboard, \"Pq38ewzLVVVRcM955QK+7UVJIBA=\");\n_c4 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"EnhancedImageDisplay\");\n$RefreshReg$(_c2, \"ImageCard\");\n$RefreshReg$(_c3, \"VideoCard\");\n$RefreshReg$(_c4, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "Container", "Row", "Col", "Card", "Tabs", "Tab", "Form", "<PERSON><PERSON>", "Plot", "ChartContainer", "DefectMap", "jsPDF", "autoTable", "XLSX", "saveAs", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getImageUrlForDisplay", "imageData", "imageType", "console", "log", "fullUrlField", "urlParts", "split", "bucketIndex", "findIndex", "part", "includes", "length", "s3Key", "slice", "join", "proxyUrl", "encodeURIComponent", "s3KeyField", "<PERSON><PERSON><PERSON>", "map", "url", "gridfsIdField", "EnhancedImageDisplay", "alt", "className", "style", "onError", "_s", "currentImageUrl", "setCurrentImageUrl", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "fallback<PERSON><PERSON><PERSON>s", "set<PERSON><PERSON>back<PERSON>tte<PERSON>s", "imageUrl", "generatedUrl", "s3KeyValue", "fullUrlValue", "handleImageError", "event", "_event$target", "_event$target2", "_event$target3", "_event$target4", "_event$target5", "error", "target", "src", "naturalWidth", "naturalHeight", "complete", "fetch", "method", "then", "response", "status", "statusText", "headers", "Object", "fromEntries", "entries", "catch", "fetchError", "message", "fallbackUrl", "getFallbackImageUrl", "alternativeUrl", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "loading", "onLoad", "_c", "ImageCard", "defect", "defectType", "defectId<PERSON><PERSON>", "_s2", "isOriginal", "setIsOriginal", "to<PERSON><PERSON><PERSON><PERSON>", "showOriginal", "isMultiDefect", "detected_defects", "detectedDefects", "md", "Header", "crack_type", "crack_id", "condition", "kerb_id", "pothole_id", "filter", "d", "Body", "maxHeight", "warn", "area_cm2", "toFixed", "depth_cm", "volume", "area_range", "kerb_type", "length_m", "username", "timestamp", "Date", "toLocaleString", "timeZone", "variant", "size", "onClick", "image_id", "Math", "random", "_c2", "VideoCard", "video", "handleDownload", "videoType", "videoId", "_id", "video_id", "downloadUrl", "ok", "errorData", "json", "Error", "videoBlob", "blob", "type", "blobUrl", "URL", "createObjectURL", "filename", "substring", "link", "document", "createElement", "href", "download", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "revokeObjectURL", "alert", "handleExport", "format", "exportFormat", "toLowerCase", "data", "pdf_data", "byteCharacters", "atob", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "Blob", "setAttribute", "visibility", "csv_data", "csv<PERSON><PERSON>nt", "row", "detectionCounts", "detection_counts", "totalDetections", "total", "models_run", "representative_frame", "e", "height", "backgroundColor", "paddingLeft", "potholes", "cracks", "kerbs", "disabled", "original_video_url", "processed_video_url", "_c3", "Dashboard", "user", "_s3", "_dashboardData$videos", "_dashboardData$videos2", "_dashboardData$videos3", "_dashboardData$users2", "_dashboardData$users3", "_dashboardData$users4", "statistics", "setStatistics", "potholesDetected", "cracksDetected", "kerbsDetected", "totalUsers", "weeklyData", "setWeeklyData", "days", "issues", "issuesByType", "setIssuesByType", "types", "counts", "dashboardData", "setDashboardData", "count", "by_size", "avg_volume", "latest", "by_type", "by_condition", "users", "by_role", "videos", "setLoading", "setError", "startDate", "setStartDate", "endDate", "setEndDate", "dateFilterApplied", "setDateFilterApplied", "usersList", "setUsersList", "selected<PERSON>ser", "setSelectedUser", "userFilterApplied", "setUserFilterApplied", "defectFilters", "setDefectFilters", "filteredIssuesByType", "setFilteredIssuesByType", "activeTab", "setActiveTab", "currentDate", "lastWeek", "setDate", "getDate", "formattedEndDate", "toISOString", "formattedStartDate", "fetchData", "filters", "params", "start_date", "end_date", "role", "user_role", "statsResponse", "get", "success", "issues_by_type", "weeklyResponse", "typesResponse", "dashboardResponse", "enhancedError", "multiDefectStats", "totalImages", "multiDefectImages", "singleDefectImages", "categoryBreakdown", "for<PERSON>ach", "category", "categoryImages", "item", "multi_defect_image", "usersResponse", "prevStats", "total_users", "prevData", "roles_distribution", "recent_users", "userErr", "err", "fetchUsers", "handleApplyDateFilter", "undefined", "handleResetDateFilter", "newEndDate", "newStartDate", "handleApplyUserFilter", "handleResetUserFilter", "handleUserChange", "value", "filteredTypes", "filteredCounts", "index", "push", "handleDefectFilterChange", "prev", "handleDownloadPDF", "_dashboardData$users", "doc", "yPosition", "setFontSize", "setFont", "text", "align", "toLocaleDateString", "totalIssues", "potholePercent", "crackPercent", "kerb<PERSON>ercent", "userTableData", "idx", "last_login", "head", "startY", "margin", "top", "styles", "fontSize", "headStyles", "fillColor", "lastAutoTable", "finalY", "potholeTableData", "crackTableData", "kerbTableData", "pageCount", "internal", "getNumberOfPages", "setPage", "pageSize", "save", "handleDownloadExcel", "wsData", "area", "depth", "ws", "utils", "aoa_to_sheet", "wb", "book_new", "book_append_sheet", "wbout", "write", "bookType", "fluid", "lg", "Group", "Label", "Control", "onChange", "Select", "active<PERSON><PERSON>", "onSelect", "k", "eventKey", "title", "x", "y", "mode", "marker", "color", "layout", "xaxis", "yaxis", "tickangle", "automargin", "t", "b", "l", "r", "showLegend", "legendItems", "label", "checked", "labels", "values", "colors", "textinfo", "insidetextorientation", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultActiveKey", "overflowY", "paddingRight", "pothole", "crack", "kerb", "keys", "colSpan", "_c4", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LTAGIT/LTA/frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { Container, Row, Col, Card, Tabs, Tab, Form, Button } from 'react-bootstrap';\r\nimport Plot from 'react-plotly.js';\r\nimport ChartContainer from '../components/ChartContainer';\r\nimport DefectMap from '../components/DefectMap';\r\nimport './dashboard.css';\r\nimport jsPDF from 'jspdf';\r\nimport autoTable from 'jspdf-autotable';\r\nimport * as XLSX from 'xlsx';\r\nimport { saveAs } from 'file-saver';\r\n\r\n/**\r\n * Comprehensive Image URL Resolution Logic\r\n * Handles both S3 URLs (new data) and GridFS IDs (legacy data)\r\n *\r\n * Priority order:\r\n * 1. S3 Full URL (direct HTTPS link)\r\n * 2. S3 Key (generate URL via API)\r\n * 3. GridFS ID (legacy endpoint)\r\n * 4. Fallback to \"No image available\"\r\n */\r\nconst getImageUrlForDisplay = (imageData, imageType = 'original') => {\r\n  console.log('getImageUrlForDisplay called:', { imageData, imageType });\r\n\r\n  if (!imageData) {\r\n    console.log('No imageData provided');\r\n    return null;\r\n  }\r\n\r\n  // Try S3 full URL first (new images with pre-generated URLs) - proxy through backend\r\n  const fullUrlField = `${imageType}_image_full_url`;\r\n  if (imageData[fullUrlField]) {\r\n    console.log('Using full URL field:', fullUrlField, imageData[fullUrlField]);\r\n    // Extract S3 key from full URL and use proxy endpoint\r\n    const urlParts = imageData[fullUrlField].split('/');\r\n    const bucketIndex = urlParts.findIndex(part => part.includes('.s3.'));\r\n    if (bucketIndex !== -1 && bucketIndex + 1 < urlParts.length) {\r\n      const s3Key = urlParts.slice(bucketIndex + 1).join('/');\r\n      const proxyUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n      console.log('Generated proxy URL from full URL:', proxyUrl);\r\n      return proxyUrl;\r\n    }\r\n  }\r\n\r\n  // Try S3 key with proxy endpoint (new images without full URL)\r\n  const s3KeyField = `${imageType}_image_s3_url`;\r\n  if (imageData[s3KeyField]) {\r\n    console.log('Using S3 key field:', s3KeyField, imageData[s3KeyField]);\r\n\r\n    // Properly encode the S3 key for URL path\r\n    const s3Key = imageData[s3KeyField];\r\n    const encodedKey = s3Key.split('/').map(part => encodeURIComponent(part)).join('/');\r\n    const url = `/api/pavement/get-s3-image/${encodedKey}`;\r\n\r\n    console.log('Generated proxy URL from S3 key:', url);\r\n    console.log('Original S3 key:', s3Key);\r\n    console.log('Encoded S3 key:', encodedKey);\r\n\r\n    return url;\r\n  }\r\n\r\n  // Fall back to GridFS endpoint (legacy images)\r\n  const gridfsIdField = `${imageType}_image_id`;\r\n  if (imageData[gridfsIdField]) {\r\n    console.log('Using GridFS field:', gridfsIdField, imageData[gridfsIdField]);\r\n    const url = `/api/pavement/get-image/${imageData[gridfsIdField]}`;\r\n    console.log('Generated GridFS URL:', url);\r\n    return url;\r\n  }\r\n\r\n  // No image URL available\r\n  console.log('No image URL available for:', imageType, imageData);\r\n  return null;\r\n};\r\n\r\n/**\r\n * Enhanced Image Component with comprehensive error handling\r\n * Supports S3 URLs, GridFS fallback, and graceful error handling\r\n */\r\nconst EnhancedImageDisplay = ({ imageData, imageType = 'original', alt, className, style, onError }) => {\r\n  const [currentImageUrl, setCurrentImageUrl] = useState(null);\r\n  const [hasError, setHasError] = useState(false);\r\n  const [fallbackAttempts, setFallbackAttempts] = useState(0);\r\n\r\n  useEffect(() => {\r\n    // Reset state when imageData changes\r\n    setHasError(false);\r\n    setFallbackAttempts(0);\r\n\r\n    // Get initial image URL\r\n    const imageUrl = getImageUrlForDisplay(imageData, imageType);\r\n\r\n    // Debug logging\r\n    console.log('EnhancedImageDisplay Debug:', {\r\n      imageType,\r\n      imageData,\r\n      generatedUrl: imageUrl,\r\n      s3KeyField: `${imageType}_image_s3_url`,\r\n      s3KeyValue: imageData?.[`${imageType}_image_s3_url`],\r\n      fullUrlField: `${imageType}_image_full_url`,\r\n      fullUrlValue: imageData?.[`${imageType}_image_full_url`]\r\n    });\r\n\r\n    setCurrentImageUrl(imageUrl);\r\n  }, [imageData, imageType]);\r\n\r\n  const handleImageError = (event) => {\r\n    console.error('🚨 Image load error:', {\r\n      imageType,\r\n      currentImageUrl,\r\n      fallbackAttempts,\r\n      error: event?.target?.error,\r\n      src: event?.target?.src,\r\n      naturalWidth: event?.target?.naturalWidth,\r\n      naturalHeight: event?.target?.naturalHeight,\r\n      complete: event?.target?.complete\r\n    });\r\n\r\n    // Test if the URL is reachable\r\n    if (currentImageUrl) {\r\n      fetch(currentImageUrl, { method: 'HEAD' })\r\n        .then(response => {\r\n          console.log('🔍 URL HEAD check:', {\r\n            url: currentImageUrl,\r\n            status: response.status,\r\n            statusText: response.statusText,\r\n            headers: Object.fromEntries(response.headers.entries())\r\n          });\r\n        })\r\n        .catch(fetchError => {\r\n          console.error('🚨 URL HEAD check failed:', {\r\n            url: currentImageUrl,\r\n            error: fetchError.message\r\n          });\r\n        });\r\n    }\r\n\r\n    if (fallbackAttempts === 0) {\r\n      // First error: try alternative image type or fallback\r\n      const fallbackUrl = getFallbackImageUrl(imageData, imageType);\r\n      console.log('🔄 Trying fallback URL:', fallbackUrl);\r\n\r\n      if (fallbackUrl && fallbackUrl !== currentImageUrl) {\r\n        setCurrentImageUrl(fallbackUrl);\r\n        setFallbackAttempts(1);\r\n        return;\r\n      }\r\n    }\r\n\r\n    // All fallbacks failed\r\n    console.error('❌ All image loading attempts failed for:', imageType);\r\n    setHasError(true);\r\n    if (onError) onError();\r\n  };\r\n\r\n  const getFallbackImageUrl = (imageData, imageType) => {\r\n    console.log('🔄 Getting fallback URL for:', imageType, imageData);\r\n\r\n    // Try direct S3 URL if we have the full URL field\r\n    const fullUrlField = `${imageType}_image_full_url`;\r\n    if (imageData[fullUrlField]) {\r\n      console.log('🔄 Trying direct S3 URL:', imageData[fullUrlField]);\r\n      return imageData[fullUrlField];\r\n    }\r\n\r\n    // Try GridFS if S3 failed\r\n    const gridfsIdField = `${imageType}_image_id`;\r\n    if (imageData[gridfsIdField]) {\r\n      console.log('🔄 Trying GridFS URL:', imageData[gridfsIdField]);\r\n      return `/api/pavement/get-image/${imageData[gridfsIdField]}`;\r\n    }\r\n\r\n    // Try alternative S3 proxy with different encoding\r\n    const s3KeyField = `${imageType}_image_s3_url`;\r\n    if (imageData[s3KeyField]) {\r\n      console.log('🔄 Trying alternative S3 proxy encoding');\r\n      const s3Key = imageData[s3KeyField];\r\n      const alternativeUrl = `/api/pavement/get-s3-image/${encodeURIComponent(s3Key)}`;\r\n      console.log('🔄 Alternative proxy URL:', alternativeUrl);\r\n      return alternativeUrl;\r\n    }\r\n\r\n    console.log('❌ No fallback URL available');\r\n    return null;\r\n  };\r\n\r\n  if (hasError || !currentImageUrl) {\r\n    return (\r\n      <div className={`text-muted d-flex align-items-center justify-content-center ${className}`} style={style}>\r\n        <div className=\"text-center\">\r\n          <i className=\"fas fa-image-slash fa-2x mb-2\"></i>\r\n          <div>No image available</div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"position-relative\">\r\n      <img\r\n        src={currentImageUrl}\r\n        alt={alt}\r\n        className={className}\r\n        style={style}\r\n        onError={handleImageError}\r\n        loading=\"lazy\"\r\n        onLoad={() => {\r\n          console.log('✅ Image loaded successfully:', currentImageUrl);\r\n          setHasError(false);\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\n// ImageCard component to isolate state for each image\r\nconst ImageCard = ({ defect, defectType, defectIdKey }) => {\r\n  const [isOriginal, setIsOriginal] = useState(false);\r\n\r\n  // Safety check: return null if defect is not provided\r\n  if (!defect) {\r\n    return null;\r\n  }\r\n\r\n  const toggleView = (showOriginal) => {\r\n    setIsOriginal(showOriginal);\r\n  };\r\n\r\n  // Check if this is a multi-defect image\r\n  const isMultiDefect = defect.detected_defects && defect.detected_defects.length > 1;\r\n  const detectedDefects = defect.detected_defects || [];\r\n  \r\n  return (\r\n    <Col md={4} className=\"mb-4\" key={`${defectType}-${defect[defectIdKey] || defect.image_id || Math.random()}`}>\r\n      <Card className={`h-100 shadow-sm ${isMultiDefect ? 'border-warning' : ''}`}>\r\n        <Card.Header className={isMultiDefect ? 'bg-warning bg-opacity-10' : ''}>\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <h6 className=\"mb-0\">\r\n              {defectType === 'cracks' ? `${defect.crack_type || 'Crack'} #${defect.crack_id || 'N/A'}` :\r\n               defectType === 'kerbs' ? `${defect.condition || 'Kerb'} #${defect.kerb_id || 'N/A'}` :\r\n               `Pothole #${defect.pothole_id || 'N/A'}`}\r\n            </h6>\r\n            {isMultiDefect && (\r\n              <small className=\"text-warning fw-bold\">\r\n                🔀 Multi-Defect\r\n              </small>\r\n            )}\r\n          </div>\r\n          {isMultiDefect && (\r\n            <div className=\"mt-1\">\r\n              <small className=\"text-muted\">\r\n                Also contains: {detectedDefects.filter(d => d !== defectType).join(', ')}\r\n              </small>\r\n            </div>\r\n          )}\r\n        </Card.Header>\r\n        <Card.Body>\r\n          <div className=\"mb-2 text-center\">\r\n            <EnhancedImageDisplay\r\n              imageData={defect}\r\n              imageType={isOriginal ? 'original' : 'processed'}\r\n              alt={`${defectType === 'cracks' ? 'Crack' : defectType === 'kerbs' ? 'Kerb' : 'Pothole'} ${defect[defectIdKey]}`}\r\n              className=\"img-fluid mb-2 border\"\r\n              style={{ maxHeight: \"200px\" }}\r\n              onError={() => {\r\n                console.warn(`Failed to load ${isOriginal ? 'original' : 'processed'} image for ${defectType} ${defect[defectIdKey]}`);\r\n              }}\r\n            />\r\n          </div>\r\n          <div className=\"small\">\r\n            {defectType === 'potholes' && (\r\n              <>\r\n                <p className=\"mb-1\"><strong>Area:</strong> {defect.area_cm2 ? defect.area_cm2.toFixed(2) : 'N/A'} cm²</p>\r\n                <p className=\"mb-1\"><strong>Depth:</strong> {defect.depth_cm ? defect.depth_cm.toFixed(2) : 'N/A'} cm</p>\r\n                <p className=\"mb-1\"><strong>Volume:</strong> {defect.volume ? defect.volume.toFixed(2) : 'N/A'}</p>\r\n              </>\r\n            )}\r\n            {defectType === 'cracks' && (\r\n              <>\r\n                <p className=\"mb-1\"><strong>Type:</strong> {defect.crack_type || 'N/A'}</p>\r\n                <p className=\"mb-1\"><strong>Area:</strong> {defect.area_cm2 ? defect.area_cm2.toFixed(2) : 'N/A'} cm²</p>\r\n                <p className=\"mb-1\"><strong>Range:</strong> {defect.area_range || 'N/A'}</p>\r\n              </>\r\n            )}\r\n            {defectType === 'kerbs' && (\r\n              <>\r\n                <p className=\"mb-1\"><strong>Type:</strong> {defect.kerb_type || 'N/A'}</p>\r\n                <p className=\"mb-1\"><strong>Length:</strong> {defect.length_m ? defect.length_m.toFixed(2) : 'N/A'} m</p>\r\n                <p className=\"mb-1\"><strong>Condition:</strong> {defect.condition || 'N/A'}</p>\r\n              </>\r\n            )}\r\n            <p className=\"mb-1\"><strong>Uploaded by:</strong> {defect.username || 'Unknown'}</p>\r\n            <p className=\"mb-1\"><strong>Timestamp:</strong> {defect.timestamp ? new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) : 'N/A'}</p>\r\n            <div className=\"mt-2\">\r\n              <Button\r\n                variant={isOriginal ? 'primary' : 'outline-primary'}\r\n                size=\"sm\"\r\n                className=\"me-2\"\r\n                onClick={() => toggleView(true)}\r\n              >\r\n                Original\r\n              </Button>\r\n              <Button\r\n                variant={!isOriginal ? 'success' : 'outline-success'}\r\n                size=\"sm\"\r\n                onClick={() => toggleView(false)}\r\n              >\r\n                Processed\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card.Body>\r\n      </Card>\r\n    </Col>\r\n  );\r\n};\r\n\r\n// VideoCard component for displaying processed videos with representative frames\r\nconst VideoCard = ({ video }) => {\r\n  // Safety check: return null if video is not provided\r\n  if (!video) {\r\n    return null;\r\n  }\r\n\r\n  const handleDownload = async (videoType) => {\r\n    try {\r\n      // Use the MongoDB _id for the download endpoint\r\n      const videoId = video._id || video.video_id;\r\n      const downloadUrl = `/api/pavement/get-s3-video/${videoId}/${videoType}`;\r\n\r\n      console.log(`🔄 Starting ${videoType} video download for ID: ${videoId}`);\r\n\r\n      // Fetch the video data as a blob to ensure proper binary handling\r\n      const response = await fetch(downloadUrl, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Accept': 'video/mp4, video/*, */*'\r\n        }\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n      }\r\n\r\n      // Get the video data as a blob\r\n      const videoBlob = await response.blob();\r\n      console.log(`✅ Downloaded ${videoType} video blob - Size: ${videoBlob.size} bytes, Type: ${videoBlob.type}`);\r\n\r\n      // Create a blob URL and trigger download\r\n      const blobUrl = URL.createObjectURL(videoBlob);\r\n\r\n      // Generate filename with proper extension\r\n      const filename = `${videoType}_video_${(video.video_id || videoId).substring(0, 8)}.mp4`;\r\n\r\n      // Create and trigger download link\r\n      const link = document.createElement('a');\r\n      link.href = blobUrl;\r\n      link.download = filename;\r\n      link.style.display = 'none';\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n\r\n      // Clean up the blob URL after a short delay to ensure download starts\r\n      setTimeout(() => {\r\n        URL.revokeObjectURL(blobUrl);\r\n        console.log(`🧹 Cleaned up blob URL for ${videoType} video`);\r\n      }, 1000);\r\n\r\n    } catch (error) {\r\n      console.error(`❌ Error downloading ${videoType} video:`, error);\r\n      alert(`Error downloading ${videoType} video: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  const handleExport = async (format) => {\r\n    try {\r\n      const exportFormat = format.toLowerCase();\r\n      const videoId = video._id || video.video_id;\r\n\r\n      // Call backend API for export with detailed detection tables\r\n      const response = await fetch(`/api/dashboard/video-processing-export?format=${exportFormat}&video_id=${videoId}`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`Export failed: ${response.statusText}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      if (exportFormat === 'pdf') {\r\n        // Handle PDF download\r\n        if (data.pdf_data) {\r\n          const byteCharacters = atob(data.pdf_data);\r\n          const byteNumbers = new Array(byteCharacters.length);\r\n          for (let i = 0; i < byteCharacters.length; i++) {\r\n            byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n          }\r\n          const byteArray = new Uint8Array(byteNumbers);\r\n          const blob = new Blob([byteArray], { type: 'application/pdf' });\r\n\r\n          const link = document.createElement('a');\r\n          const url = URL.createObjectURL(blob);\r\n          link.setAttribute('href', url);\r\n          link.setAttribute('download', `video_${(video.video_id || videoId).substring(0, 8)}_detailed_report.pdf`);\r\n          link.style.visibility = 'hidden';\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          URL.revokeObjectURL(url);\r\n        }\r\n      } else if (exportFormat === 'csv') {\r\n        // Handle CSV download\r\n        if (data.csv_data) {\r\n          const csvContent = data.csv_data.map(row => row.join(',')).join('\\n');\r\n          const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n\r\n          const link = document.createElement('a');\r\n          const url = URL.createObjectURL(blob);\r\n          link.setAttribute('href', url);\r\n          link.setAttribute('download', `video_${(video.video_id || videoId).substring(0, 8)}_detailed_report.csv`);\r\n          link.style.visibility = 'hidden';\r\n          document.body.appendChild(link);\r\n          link.click();\r\n          document.body.removeChild(link);\r\n          URL.revokeObjectURL(url);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(`Error exporting to ${format}:`, error);\r\n      alert(`Error exporting to ${format}: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  const detectionCounts = video.detection_counts || {};\r\n  const totalDetections = detectionCounts.total || 0;\r\n\r\n  return (\r\n    <Col md={4} className=\"mb-4\" key={`video-${video.video_id}`}>\r\n      <Card className=\"h-100 shadow-sm\">\r\n        <Card.Header className=\"bg-info bg-opacity-10\">\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <h6 className=\"mb-0\">\r\n              Video #{video.video_id.substring(0, 8)}...\r\n            </h6>\r\n            <small className=\"text-info fw-bold\">\r\n              📹 Video\r\n            </small>\r\n          </div>\r\n          <div className=\"mt-1\">\r\n            <small className=\"text-muted\">\r\n              Models: {video.models_run ? video.models_run.join(', ') : 'N/A'}\r\n            </small>\r\n          </div>\r\n        </Card.Header>\r\n        <Card.Body>\r\n          <div className=\"mb-2 text-center\">\r\n            {video.representative_frame ? (\r\n              <img\r\n                src={`data:image/jpeg;base64,${video.representative_frame}`}\r\n                alt=\"Video thumbnail\"\r\n                className=\"img-fluid mb-2 border\"\r\n                style={{ maxHeight: \"200px\" }}\r\n                onError={(e) => {\r\n                  console.warn(`Failed to load representative frame for video ${video.video_id}`);\r\n                  e.target.style.display = 'none';\r\n                }}\r\n              />\r\n            ) : (\r\n              <div className=\"d-flex align-items-center justify-content-center border\" style={{ height: \"200px\", backgroundColor: \"#f8f9fa\" }}>\r\n                <span className=\"text-muted\">No thumbnail available</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n          <div className=\"small\">\r\n            <p className=\"mb-1\"><strong>Detections:</strong></p>\r\n            <ul className=\"mb-2\" style={{ paddingLeft: '20px' }}>\r\n              <li>Potholes: {detectionCounts.potholes || 0}</li>\r\n              <li>Cracks: {detectionCounts.cracks || 0}</li>\r\n              <li>Kerbs: {detectionCounts.kerbs || 0}</li>\r\n            </ul>\r\n            <p className=\"mb-1\"><strong>Total Detections:</strong> {totalDetections}</p>\r\n            <p className=\"mb-1\"><strong>Uploaded by:</strong> {video.username || 'Unknown'}</p>\r\n            <p className=\"mb-1\"><strong>Timestamp:</strong> {video.timestamp ? new Date(video.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' }) : 'N/A'}</p>\r\n\r\n            {/* Download Buttons */}\r\n            <div className=\"mt-2 mb-2\">\r\n              <Button\r\n                variant=\"primary\"\r\n                size=\"sm\"\r\n                className=\"me-2 mb-1\"\r\n                onClick={() => handleDownload('original')}\r\n                disabled={!video.original_video_url}\r\n              >\r\n                📥 Original Video\r\n              </Button>\r\n              <Button\r\n                variant=\"success\"\r\n                size=\"sm\"\r\n                className=\"me-2 mb-1\"\r\n                onClick={() => handleDownload('processed')}\r\n                disabled={!video.processed_video_url}\r\n              >\r\n                📥 Processed Video\r\n              </Button>\r\n            </div>\r\n\r\n            {/* Export Buttons */}\r\n            <div className=\"mt-2\">\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                className=\"me-2\"\r\n                onClick={() => handleExport('PDF')}\r\n              >\r\n                📄 Export PDF\r\n              </Button>\r\n              <Button\r\n                variant=\"outline-secondary\"\r\n                size=\"sm\"\r\n                onClick={() => handleExport('CSV')}\r\n              >\r\n                📊 Export CSV\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </Card.Body>\r\n      </Card>\r\n    </Col>\r\n  );\r\n};\r\n\r\nfunction Dashboard({ user }) {\r\n  const [statistics, setStatistics] = useState({\r\n    potholesDetected: 0,\r\n    cracksDetected: 0,\r\n    kerbsDetected: 0,\r\n    totalUsers: 0\r\n  });\r\n  const [weeklyData, setWeeklyData] = useState({\r\n    days: [],\r\n    issues: []\r\n  });\r\n  const [issuesByType, setIssuesByType] = useState({\r\n    types: [],\r\n    counts: []\r\n  });\r\n  const [dashboardData, setDashboardData] = useState({\r\n    potholes: {\r\n      count: 0,\r\n      by_size: {},\r\n      avg_volume: 0,\r\n      latest: []\r\n    },\r\n    cracks: {\r\n      count: 0,\r\n      by_type: {},\r\n      by_size: {},\r\n      latest: []\r\n    },\r\n    kerbs: {\r\n      count: 0,\r\n      by_condition: {},\r\n      latest: []\r\n    },\r\n    users: {\r\n      count: 0,\r\n      by_role: {},\r\n      latest: []\r\n    },\r\n    videos: {\r\n      count: 0,\r\n      latest: []\r\n    }\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  \r\n  // Date filter state\r\n  const [startDate, setStartDate] = useState('');\r\n  const [endDate, setEndDate] = useState('');\r\n  const [dateFilterApplied, setDateFilterApplied] = useState(false);\r\n\r\n  // User filter state\r\n  const [usersList, setUsersList] = useState([]);\r\n  const [selectedUser, setSelectedUser] = useState('');\r\n  const [userFilterApplied, setUserFilterApplied] = useState(false);\r\n\r\n  // Defect type filter state\r\n  const [defectFilters, setDefectFilters] = useState({\r\n    potholes: true,\r\n    cracks: true,\r\n    kerbs: true\r\n  });\r\n  \r\n  // Filtered issues state\r\n  const [filteredIssuesByType, setFilteredIssuesByType] = useState({\r\n    types: [],\r\n    counts: []\r\n  });\r\n  \r\n  // Dashboard tab state\r\n  const [activeTab, setActiveTab] = useState('dashboard');\r\n\r\n  // Set default date range to previous week and auto-apply filter\r\n  useEffect(() => {\r\n    const currentDate = new Date();\r\n    const lastWeek = new Date();\r\n    lastWeek.setDate(currentDate.getDate() - 6); // 6 days ago + today = 7 days\r\n\r\n    const formattedEndDate = currentDate.toISOString().split('T')[0];\r\n    const formattedStartDate = lastWeek.toISOString().split('T')[0];\r\n\r\n    setEndDate(formattedEndDate);\r\n    setStartDate(formattedStartDate);\r\n    fetchData({ startDate: formattedStartDate, endDate: formattedEndDate });\r\n  }, []);\r\n\r\n  // Fetch dashboard data from backend\r\n  const fetchData = async (filters = {}) => {\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Add filters to requests if provided\r\n      const params = {};\r\n      if (filters.startDate) params.start_date = filters.startDate;\r\n      if (filters.endDate) params.end_date = filters.endDate;\r\n      if (filters.username) params.username = filters.username;\r\n      if (user?.role) params.user_role = user.role;\r\n      \r\n      // Get overview statistics\r\n      const statsResponse = await axios.get('/api/dashboard/statistics', { params });\r\n      if (statsResponse.data.success) {\r\n        setStatistics({\r\n          potholesDetected: statsResponse.data.data.issues_by_type.potholes,\r\n          cracksDetected: statsResponse.data.data.issues_by_type.cracks,\r\n          kerbsDetected: statsResponse.data.data.issues_by_type.kerbs,\r\n          totalUsers: statistics.totalUsers // Preserve the existing user count\r\n        });\r\n      }\r\n      \r\n      // Get weekly trend data\r\n      const weeklyResponse = await axios.get('/api/dashboard/weekly-trend', { params });\r\n      setWeeklyData({\r\n        days: weeklyResponse.data.days,\r\n        issues: weeklyResponse.data.issues\r\n      });\r\n      \r\n      // Get issues by type\r\n      const typesResponse = await axios.get('/api/dashboard/issues-by-type', { params });\r\n      setIssuesByType({\r\n        types: typesResponse.data.types,\r\n        counts: typesResponse.data.counts\r\n      });\r\n      \r\n      // Get detailed dashboard data including latest images with enhanced S3-MongoDB integration\r\n      let dashboardResponse;\r\n      try {\r\n        // Try enhanced API endpoint first (with comprehensive S3-MongoDB integration)\r\n        dashboardResponse = await axios.get('/api/dashboard/summary-v2', { params });\r\n        console.log('✅ Using enhanced dashboard API with comprehensive S3-MongoDB integration');\r\n      } catch (enhancedError) {\r\n        console.warn('⚠️ Enhanced API not available, falling back to standard API:', enhancedError.message);\r\n        // Fallback to original API\r\n        dashboardResponse = await axios.get('/api/dashboard/summary', { params });\r\n        console.log('✅ Using standard dashboard API');\r\n      }\r\n\r\n      if (dashboardResponse.data.success) {\r\n        const dashboardData = dashboardResponse.data.data;\r\n\r\n        // Calculate multi-defect statistics\r\n        const multiDefectStats = {\r\n          totalImages: 0,\r\n          multiDefectImages: 0,\r\n          singleDefectImages: 0,\r\n          categoryBreakdown: {\r\n            potholes: 0,\r\n            cracks: 0,\r\n            kerbs: 0\r\n          }\r\n        };\r\n        \r\n        // Count multi-defect images from each category\r\n        ['potholes', 'cracks', 'kerbs'].forEach(category => {\r\n          const categoryImages = dashboardData[category].latest || [];\r\n          categoryImages.forEach(item => {\r\n            if (item.multi_defect_image) {\r\n              multiDefectStats.multiDefectImages++;\r\n            }\r\n            multiDefectStats.totalImages++;\r\n            multiDefectStats.categoryBreakdown[category]++;\r\n          });\r\n        });\r\n        \r\n        multiDefectStats.singleDefectImages = multiDefectStats.totalImages - multiDefectStats.multiDefectImages;\r\n        \r\n        // Add multi-defect statistics to dashboard data\r\n        dashboardData.multiDefectStats = multiDefectStats;\r\n        \r\n        setDashboardData(dashboardData);\r\n      }\r\n      \r\n      // Get users data\r\n      try {\r\n        const usersResponse = await axios.get('/api/users/summary', { params });\r\n        if (usersResponse.data.success) {\r\n          setStatistics(prevStats => ({\r\n            ...prevStats,\r\n            totalUsers: usersResponse.data.total_users || 0\r\n          }));\r\n          \r\n          // Ensure users data is properly set in dashboardData\r\n          setDashboardData(prevData => ({\r\n            ...prevData,\r\n            users: {\r\n              count: usersResponse.data.total_users || 0,\r\n              by_role: usersResponse.data.roles_distribution || {},\r\n              latest: usersResponse.data.recent_users || []\r\n            }\r\n          }));\r\n        }\r\n      } catch (userErr) {\r\n        console.error('Error fetching user data:', userErr);\r\n        // Non-critical error, continue with other data\r\n      }\r\n      \r\n      setLoading(false);\r\n    } catch (err) {\r\n      setError('Error fetching dashboard data');\r\n      setLoading(false);\r\n      console.error('Error fetching data:', err);\r\n    }\r\n  };\r\n\r\n  // Fetch users list for filter dropdown\r\n  useEffect(() => {\r\n    const fetchUsers = async () => {\r\n      try {\r\n        const params = {};\r\n        if (user?.role) params.user_role = user.role;\r\n        \r\n        const response = await axios.get('/api/users/all', { params });\r\n        if (response.data.success) {\r\n          // Users are already filtered by the backend based on RBAC\r\n          setUsersList(response.data.users);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching users:', error);\r\n      }\r\n    };\r\n    \r\n    fetchUsers();\r\n  }, [user]);\r\n\r\n  // Handle date filter application\r\n  const handleApplyDateFilter = () => {\r\n    if (startDate && endDate) {\r\n      fetchData({ \r\n        startDate, \r\n        endDate,\r\n        username: selectedUser || undefined\r\n      });\r\n      setDateFilterApplied(true);\r\n    }\r\n  };\r\n\r\n  // Handle date filter reset\r\n  const handleResetDateFilter = () => {\r\n    const currentDate = new Date();\r\n    const lastWeek = new Date();\r\n    lastWeek.setDate(currentDate.getDate() - 6); // 6 days ago + today = 7 days\r\n\r\n    const newEndDate = currentDate.toISOString().split('T')[0];\r\n    const newStartDate = lastWeek.toISOString().split('T')[0];\r\n    \r\n    setEndDate(newEndDate);\r\n    setStartDate(newStartDate);\r\n    fetchData({ \r\n      startDate: newStartDate, \r\n      endDate: newEndDate,\r\n      username: selectedUser || undefined\r\n    });\r\n    setDateFilterApplied(false);\r\n  };\r\n\r\n  // Handle user filter application\r\n  const handleApplyUserFilter = () => {\r\n    fetchData({\r\n      startDate,\r\n      endDate,\r\n      username: selectedUser || undefined\r\n    });\r\n    setUserFilterApplied(!!selectedUser);\r\n  };\r\n\r\n  // Handle user filter reset\r\n  const handleResetUserFilter = () => {\r\n    setSelectedUser('');\r\n    fetchData({\r\n      startDate,\r\n      endDate\r\n    });\r\n    setUserFilterApplied(false);\r\n  };\r\n\r\n  // Handle user selection\r\n  const handleUserChange = (e) => {\r\n    setSelectedUser(e.target.value);\r\n  };\r\n\r\n  // Filter the issues by type whenever the filters or data changes\r\n  useEffect(() => {\r\n    if (issuesByType.types.length > 0) {\r\n      const filteredTypes = [];\r\n      const filteredCounts = [];\r\n      \r\n      issuesByType.types.forEach((type, index) => {\r\n        if (\r\n          (type.includes('Pothole') && defectFilters.potholes) ||\r\n          (type.includes('Crack') && defectFilters.cracks) ||\r\n          (type.includes('Kerb') && defectFilters.kerbs)\r\n        ) {\r\n          filteredTypes.push(type);\r\n          filteredCounts.push(issuesByType.counts[index]);\r\n        }\r\n      });\r\n      \r\n      setFilteredIssuesByType({\r\n        types: filteredTypes,\r\n        counts: filteredCounts\r\n      });\r\n    }\r\n  }, [issuesByType, defectFilters]);\r\n\r\n  // Handle defect filter change\r\n  const handleDefectFilterChange = (defectType) => {\r\n    setDefectFilters(prev => ({\r\n      ...prev,\r\n      [defectType]: !prev[defectType]\r\n    }));\r\n  };\r\n\r\n  // Add export handlers\r\n  const handleDownloadPDF = () => {\r\n    const doc = new jsPDF();\r\n    let yPosition = 20;\r\n    \r\n    // Header\r\n    doc.setFontSize(20);\r\n    doc.setFont('helvetica', 'bold');\r\n    doc.text('Road AI Safety Enhancement - Dashboard Report', 105, yPosition, { align: 'center' });\r\n    yPosition += 15;\r\n    \r\n    doc.setFontSize(12);\r\n    doc.setFont('helvetica', 'normal');\r\n    doc.text(`Generated on: ${new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}`, 105, yPosition, { align: 'center' });\r\n    yPosition += 20;\r\n    \r\n    // Date Range Info\r\n    if (dateFilterApplied) {\r\n      doc.setFontSize(14);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Date Range Filter:', 14, yPosition);\r\n      yPosition += 8;\r\n      doc.setFontSize(12);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`From: ${new Date(startDate).toLocaleDateString()} To: ${new Date(endDate).toLocaleDateString()}`, 14, yPosition);\r\n      yPosition += 15;\r\n    }\r\n    \r\n    // Statistics Summary\r\n    doc.setFontSize(16);\r\n    doc.setFont('helvetica', 'bold');\r\n    doc.text('Statistics Summary', 14, yPosition);\r\n    yPosition += 10;\r\n    \r\n    doc.setFontSize(12);\r\n    doc.setFont('helvetica', 'normal');\r\n    doc.text(`Total Potholes Detected: ${statistics.potholesDetected}`, 14, yPosition);\r\n    yPosition += 7;\r\n    doc.text(`Total Cracks Detected: ${statistics.cracksDetected}`, 14, yPosition);\r\n    yPosition += 7;\r\n    doc.text(`Total Kerbs Detected: ${statistics.kerbsDetected}`, 14, yPosition);\r\n    yPosition += 7;\r\n    doc.text(`Total Users: ${statistics.totalUsers}`, 14, yPosition);\r\n    yPosition += 15;\r\n    \r\n    // Infrastructure Distribution\r\n    doc.setFontSize(16);\r\n    doc.setFont('helvetica', 'bold');\r\n    doc.text('Infrastructure Distribution', 14, yPosition);\r\n    yPosition += 10;\r\n    \r\n    const totalIssues = statistics.potholesDetected + statistics.cracksDetected + statistics.kerbsDetected;\r\n    if (totalIssues > 0) {\r\n      const potholePercent = ((statistics.potholesDetected / totalIssues) * 100).toFixed(1);\r\n      const crackPercent = ((statistics.cracksDetected / totalIssues) * 100).toFixed(1);\r\n      const kerbPercent = ((statistics.kerbsDetected / totalIssues) * 100).toFixed(1);\r\n      \r\n      doc.setFontSize(12);\r\n      doc.setFont('helvetica', 'normal');\r\n      doc.text(`Potholes: ${statistics.potholesDetected} (${potholePercent}%)`, 14, yPosition);\r\n      yPosition += 7;\r\n      doc.text(`Cracks: ${statistics.cracksDetected} (${crackPercent}%)`, 14, yPosition);\r\n      yPosition += 7;\r\n      doc.text(`Kerbs: ${statistics.kerbsDetected} (${kerbPercent}%)`, 14, yPosition);\r\n      yPosition += 15;\r\n    }\r\n    \r\n    // User Overview\r\n    if (dashboardData.users?.latest && dashboardData.users.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Recent Users', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const userTableData = dashboardData.users.latest.map((user, idx) => [\r\n        idx + 1,\r\n        user.username,\r\n        user.role,\r\n        new Date(user.last_login).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Username', 'Role', 'Last Login']],\r\n        body: userTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 10 },\r\n        headStyles: { fillColor: [0, 123, 255] }\r\n      });\r\n      \r\n      yPosition = doc.lastAutoTable.finalY + 15;\r\n    }\r\n    \r\n    // Potholes Section\r\n    if (dashboardData.potholes.latest && dashboardData.potholes.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Potholes Detected', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const potholeTableData = dashboardData.potholes.latest.map((defect, idx) => [\r\n        idx + 1,\r\n        defect.area_cm2 ? defect.area_cm2.toFixed(2) + ' cm²' : 'N/A',\r\n        defect.depth_cm ? defect.depth_cm.toFixed(2) + ' cm' : 'N/A',\r\n        defect.volume ? defect.volume.toFixed(2) : 'N/A',\r\n        defect.username || 'Unknown',\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Area', 'Depth', 'Volume', 'Uploaded By', 'Timestamp']],\r\n        body: potholeTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 9 },\r\n        headStyles: { fillColor: [220, 53, 69] }\r\n      });\r\n      \r\n      yPosition = doc.lastAutoTable.finalY + 15;\r\n    }\r\n    \r\n    // Cracks Section\r\n    if (dashboardData.cracks.latest && dashboardData.cracks.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Cracks Detected', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const crackTableData = dashboardData.cracks.latest.map((defect, idx) => [\r\n        idx + 1,\r\n        defect.crack_type || 'Unknown',\r\n        defect.area_cm2 ? defect.area_cm2.toFixed(2) + ' cm²' : 'N/A',\r\n        defect.area_range || 'N/A',\r\n        defect.username || 'Unknown',\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Type', 'Area', 'Range', 'Uploaded By', 'Timestamp']],\r\n        body: crackTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 9 },\r\n        headStyles: { fillColor: [40, 167, 69] }\r\n      });\r\n      \r\n      yPosition = doc.lastAutoTable.finalY + 15;\r\n    }\r\n    \r\n    // Kerbs Section\r\n    if (dashboardData.kerbs.latest && dashboardData.kerbs.latest.length > 0) {\r\n      doc.setFontSize(16);\r\n      doc.setFont('helvetica', 'bold');\r\n      doc.text('Kerbs Detected', 14, yPosition);\r\n      yPosition += 10;\r\n      \r\n      const kerbTableData = dashboardData.kerbs.latest.map((defect, idx) => [\r\n        idx + 1,\r\n        defect.kerb_type || 'Unknown',\r\n        defect.length_m ? defect.length_m.toFixed(2) + ' m' : 'N/A',\r\n        defect.condition || 'Unknown',\r\n        defect.username || 'Unknown',\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ]);\r\n      \r\n      autoTable(doc, {\r\n        head: [['#', 'Type', 'Length', 'Condition', 'Uploaded By', 'Timestamp']],\r\n        body: kerbTableData,\r\n        startY: yPosition,\r\n        margin: { top: 10 },\r\n        styles: { fontSize: 9 },\r\n        headStyles: { fillColor: [0, 123, 255] }\r\n      });\r\n    }\r\n    \r\n    // Footer\r\n    const pageCount = doc.internal.getNumberOfPages();\r\n    for (let i = 1; i <= pageCount; i++) {\r\n      doc.setPage(i);\r\n      doc.setFontSize(10);\r\n      doc.setFont('helvetica', 'italic');\r\n      doc.text(`Page ${i} of ${pageCount}`, 105, doc.internal.pageSize.height - 10, { align: 'center' });\r\n    }\r\n    \r\n    doc.save('Dashboard_Report.pdf');\r\n  };\r\n\r\n  const handleDownloadExcel = () => {\r\n    const wsData = [\r\n      ['#', 'Area (cm²)', 'Depth (cm)', 'Volume', 'Uploaded By', 'Timestamp'],\r\n      ...(dashboardData.potholes.latest || []).map((defect, idx) => [\r\n        idx + 1,\r\n        defect.area,\r\n        defect.depth,\r\n        defect.volume,\r\n        defect.username,\r\n        new Date(defect.timestamp).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })\r\n      ])\r\n    ];\r\n    const ws = XLSX.utils.aoa_to_sheet(wsData);\r\n    const wb = XLSX.utils.book_new();\r\n    XLSX.utils.book_append_sheet(wb, ws, 'Processed Report');\r\n    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });\r\n    saveAs(new Blob([wbout], { type: 'application/octet-stream' }), 'Processed_Report.xlsx');\r\n  };\r\n\r\n  return (\r\n    <Container fluid className=\"dashboard-container\">\r\n      {/* Filters Card */}\r\n      <Card className=\"mb-3 shadow-sm dashboard-card filters-card\">\r\n        <Card.Header className=\"bg-primary text-white py-2\">\r\n          <h6 className=\"mb-0\">Filters</h6>\r\n        </Card.Header>\r\n        <Card.Body className=\"py-3\">\r\n          <Row className=\"g-3\">\r\n            <Col lg={6}>\r\n              <div className=\"filter-section\">\r\n                <h6 className=\"mb-2\">Date Range</h6>\r\n                <div className=\"filter-controls\">\r\n                  <div className=\"filter-field\">\r\n                    <Form.Group>\r\n                      <Form.Label className=\"small mb-1\">Start Date</Form.Label>\r\n                      <Form.Control\r\n                        type=\"date\"\r\n                        value={startDate}\r\n                        onChange={(e) => setStartDate(e.target.value)}\r\n                        size=\"sm\"\r\n                      />\r\n                    </Form.Group>\r\n                  </div>\r\n                  <div className=\"filter-field\">\r\n                    <Form.Group>\r\n                      <Form.Label className=\"small mb-1\">End Date</Form.Label>\r\n                      <Form.Control\r\n                        type=\"date\"\r\n                        value={endDate}\r\n                        onChange={(e) => setEndDate(e.target.value)}\r\n                        size=\"sm\"\r\n                      />\r\n                    </Form.Group>\r\n                  </div>\r\n                  <div className=\"filter-actions\">\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"primary\"\r\n                      onClick={handleApplyDateFilter}\r\n                      disabled={!startDate || !endDate}\r\n                    >\r\n                      Apply\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"outline-secondary\"\r\n                      onClick={handleResetDateFilter}\r\n                      disabled={!dateFilterApplied}\r\n                    >\r\n                      Reset\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                {dateFilterApplied && (\r\n                  <div className=\"filter-status text-success mt-2 p-2\">\r\n                    <small>Showing data from {new Date(startDate).toLocaleDateString()} to {new Date(endDate).toLocaleDateString()}</small>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Col>\r\n            <Col lg={6}>\r\n              <div className=\"filter-section\">\r\n                <h6 className=\"mb-2\">User Filter</h6>\r\n                <div className=\"filter-controls\">\r\n                  <div className=\"filter-field\">\r\n                    <Form.Group>\r\n                      <Form.Label className=\"small mb-1\">Select User</Form.Label>\r\n                      <Form.Select\r\n                        value={selectedUser}\r\n                        onChange={handleUserChange}\r\n                        size=\"sm\"\r\n                      >\r\n                        <option value=\"\">All Users</option>\r\n                        {usersList.map((user, index) => (\r\n                          <option key={index} value={user.username}>\r\n                            {user.username} ({user.role})\r\n                          </option>\r\n                        ))}\r\n                      </Form.Select>\r\n                    </Form.Group>\r\n                  </div>\r\n                  <div className=\"filter-actions\">\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"primary\"\r\n                      onClick={handleApplyUserFilter}\r\n                    >\r\n                      Apply\r\n                    </Button>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"outline-secondary\"\r\n                      onClick={handleResetUserFilter}\r\n                      disabled={!userFilterApplied}\r\n                    >\r\n                      Reset\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n                {userFilterApplied && (\r\n                  <div className=\"filter-status text-success mt-2 p-2\">\r\n                    <small>Showing data for user: {selectedUser}</small>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Col>\r\n          </Row>\r\n        </Card.Body>\r\n      </Card>\r\n      \r\n      {loading ? (\r\n        <div className=\"text-center\">\r\n          <div className=\"spinner-border text-primary\" role=\"status\">\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      ) : error ? (\r\n        <div className=\"alert alert-danger p-3\">{error}</div>\r\n      ) : (\r\n        <>\r\n          {/* Dashboard Tabs */}\r\n          <Card className=\"mb-3 shadow-sm dashboard-card\">\r\n            <Card.Body className=\"p-0\">\r\n              <Tabs \r\n                activeKey={activeTab} \r\n                onSelect={(k) => setActiveTab(k)} \r\n                className=\"dashboard-tabs\"\r\n              >\r\n                {/* Dashboard View Tab */}\r\n                <Tab eventKey=\"dashboard\" title=\"Dashboard View\">\r\n                  <div className=\"p-3\">\r\n                    {/* Top Stats Cards */}\r\n                    <Row className=\"mb-3 g-3\">\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Potholes</h6>\r\n                            <h3 className=\"text-primary mb-0\">{statistics.potholesDetected}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Cracks</h6>\r\n                            <h3 className=\"text-primary mb-0\">{statistics.cracksDetected}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Kerbs</h6>\r\n                            <h3 className=\"text-primary mb-0\">{statistics.kerbsDetected}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={3}>\r\n                        <Card className=\"h-100 shadow-sm dashboard-card stats-card\">\r\n                          <Card.Body className=\"text-center py-3\">\r\n                            <h6 className=\"card-title mb-2\">Users</h6>\r\n                            <h3 className=\"text-success mb-0\">{statistics.totalUsers}</h3>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* Charts Row */}\r\n                    <Row className=\"mb-3 g-3\">\r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Weekly Detection Trend</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  x: weeklyData.days,\r\n                                  y: weeklyData.issues,\r\n                                  type: 'scatter',\r\n                                  mode: 'lines+markers',\r\n                                  marker: { color: '#007bff' }\r\n                                }\r\n                              ]}\r\n                              layout={{\r\n                                xaxis: { title: 'Day' },\r\n                                yaxis: { title: 'Issues Detected' }\r\n                              }}\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      \r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Issues by Type</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  type: 'bar',\r\n                                  x: filteredIssuesByType.types,\r\n                                  y: filteredIssuesByType.counts,\r\n                                  marker: {\r\n                                    color: filteredIssuesByType.types.map(type => {\r\n                                      if (type.includes('Pothole')) return '#007bff';\r\n                                      if (type.includes('Crack')) return '#28a745';\r\n                                      if (type.includes('Kerb')) return '#dc3545';\r\n                                      return '#6c757d';\r\n                                    })\r\n                                  }\r\n                                }\r\n                              ]}\r\n                              layout={{\r\n                                xaxis: { \r\n                                  title: 'Issue Type',\r\n                                  tickangle: -45,\r\n                                  automargin: true\r\n                                },\r\n                                yaxis: { title: 'Count' },\r\n                                margin: { t: 10, b: 80, l: 50, r: 10 }\r\n                              }}\r\n                              showLegend={true}\r\n                              legendItems={[\r\n                                {\r\n                                  label: 'Potholes',\r\n                                  color: '#007bff',\r\n                                  checked: defectFilters.potholes,\r\n                                  onChange: () => handleDefectFilterChange('potholes')\r\n                                },\r\n                                {\r\n                                  label: 'Cracks',\r\n                                  color: '#28a745',\r\n                                  checked: defectFilters.cracks,\r\n                                  onChange: () => handleDefectFilterChange('cracks')\r\n                                },\r\n                                {\r\n                                  label: 'Kerbs',\r\n                                  color: '#dc3545',\r\n                                  checked: defectFilters.kerbs,\r\n                                  onChange: () => handleDefectFilterChange('kerbs')\r\n                                }\r\n                              ]}\r\n                              className=\"compact-legend\"\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* Infrastructure Distribution */}\r\n                    <Row className=\"mb-3\">\r\n                      <Col md={12}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Infrastructure Distribution</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  type: 'pie',\r\n                                  labels: ['Potholes', 'Cracks', 'Kerbs'],\r\n                                  values: [\r\n                                    statistics.potholesDetected,\r\n                                    statistics.cracksDetected,\r\n                                    statistics.kerbsDetected\r\n                                  ],\r\n                                  marker: {\r\n                                    colors: ['#007bff', '#28a745', '#dc3545']\r\n                                  },\r\n                                  textinfo: \"label+percent\",\r\n                                  insidetextorientation: \"radial\"\r\n                                }\r\n                              ]}\r\n                              layout={{\r\n                                height: 300\r\n                              }}\r\n                              isPieChart={true}\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* All Uploaded Images Section */}\r\n                    <Row className=\"mb-3\">\r\n                      <Col md={12}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">All Uploaded Images</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <Tabs defaultActiveKey=\"potholes\" className=\"mb-2\">\r\n                              <Tab eventKey=\"potholes\" title={`Potholes (${dashboardData.potholes.latest.length})`}>\r\n                                <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                  <Row>\r\n                                    {dashboardData.potholes.latest.map((pothole, index) => (\r\n                                      <ImageCard\r\n                                        key={`pothole-${pothole.pothole_id || pothole.image_id || index}`}\r\n                                        defect={pothole}\r\n                                        defectType=\"potholes\"\r\n                                        defectIdKey=\"pothole_id\"\r\n                                      />\r\n                                    ))}\r\n                                  </Row>\r\n                                </div>\r\n                              </Tab>\r\n                              <Tab eventKey=\"cracks\" title={`Cracks (${dashboardData.cracks.latest.length})`}>\r\n                                <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                  <Row>\r\n                                    {dashboardData.cracks.latest.map((crack, index) => (\r\n                                      <ImageCard\r\n                                        key={`crack-${crack.crack_id || crack.image_id || index}`}\r\n                                        defect={crack}\r\n                                        defectType=\"cracks\"\r\n                                        defectIdKey=\"crack_id\"\r\n                                      />\r\n                                    ))}\r\n                                  </Row>\r\n                                </div>\r\n                              </Tab>\r\n                              <Tab eventKey=\"kerbs\" title={`Kerbs (${dashboardData.kerbs.latest.length})`}>\r\n                                <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                  <Row>\r\n                                    {dashboardData.kerbs && dashboardData.kerbs.latest && dashboardData.kerbs.latest.length > 0 ? (\r\n                                      dashboardData.kerbs.latest.map((kerb, index) => (\r\n                                        <ImageCard\r\n                                          key={`kerb-${kerb.kerb_id || kerb.image_id || index}`}\r\n                                          defect={kerb}\r\n                                          defectType=\"kerbs\"\r\n                                          defectIdKey=\"kerb_id\"\r\n                                        />\r\n                                      ))\r\n                                    ) : (\r\n                                      <Col>\r\n                                        <div className=\"alert alert-info p-3\">\r\n                                          No kerb images available yet. Upload some kerb images using the Pavement Analysis tool.\r\n                                        </div>\r\n                                      </Col>\r\n                                    )}\r\n                                  </Row>\r\n                                </div>\r\n                              </Tab>\r\n                            </Tabs>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    {/* All Videos Processed Section */}\r\n                    <Row className=\"mb-3\">\r\n                      <Col md={12}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-info text-white py-2\">\r\n                            <h6 className=\"mb-0\">All Videos Processed ({dashboardData.videos?.latest?.length || 0})</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            {dashboardData.videos?.latest && dashboardData.videos.latest.length > 0 ? (\r\n                              <div style={{ maxHeight: '700px', overflowY: 'auto', paddingRight: '10px' }}>\r\n                                <Row>\r\n                                  {dashboardData.videos.latest.map((video, index) => (\r\n                                    <VideoCard\r\n                                      key={`video-${video.video_id || index}`}\r\n                                      video={video}\r\n                                    />\r\n                                  ))}\r\n                                </Row>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"alert alert-info p-3\">\r\n                                No processed videos available yet. Upload and process some videos using the Video Defect Detection tool.\r\n                              </div>\r\n                            )}\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </Tab>\r\n\r\n                {/* Defect Map Tab */}\r\n                <Tab eventKey=\"map\" title=\"Defect Map View\">\r\n                  <div className=\"p-4\">\r\n                    <DefectMap user={user} />\r\n                  </div>\r\n                </Tab>\r\n\r\n                {/* Users Overview Tab */}\r\n                <Tab eventKey=\"users\" title=\"Users Overview\">\r\n                  <div className=\"p-3\">\r\n                    <Row className=\"g-3\">\r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">User Distribution by Role</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <ChartContainer\r\n                              data={[\r\n                                {\r\n                                  type: 'pie',\r\n                                  labels: Object.keys(dashboardData.users?.by_role || {}),\r\n                                  values: Object.values(dashboardData.users?.by_role || {}),\r\n                                  marker: {\r\n                                    colors: ['#007bff', '#28a745', '#dc3545', '#6c757d']\r\n                                  },\r\n                                  textinfo: \"label+percent\",\r\n                                  insidetextorientation: \"radial\"\r\n                                }\r\n                              ]}\r\n                              isPieChart={true}\r\n                            />\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Card className=\"shadow-sm dashboard-card\">\r\n                          <Card.Header className=\"bg-primary text-white py-2\">\r\n                            <h6 className=\"mb-0\">Recent Users</h6>\r\n                          </Card.Header>\r\n                          <Card.Body className=\"py-3\">\r\n                            <div className=\"table-responsive\">\r\n                              <table className=\"table table-sm table-hover\">\r\n                                <thead>\r\n                                  <tr>\r\n                                    <th>Username</th>\r\n                                    <th>Role</th>\r\n                                    <th>Last Login</th>\r\n                                  </tr>\r\n                                </thead>\r\n                                <tbody>\r\n                                  {dashboardData.users?.latest && dashboardData.users.latest.length > 0 ? (\r\n                                    dashboardData.users.latest.map((user, index) => (\r\n                                      <tr key={`user-${index}`}>\r\n                                        <td>{user.username}</td>\r\n                                        <td>\r\n                                          <span className={`badge bg-${\r\n                                            user.role === 'admin' ? 'danger' : \r\n                                            user.role === 'manager' ? 'warning' : \r\n                                            'primary'\r\n                                          }`}>\r\n                                            {user.role}\r\n                                          </span>\r\n                                        </td>\r\n                                        <td>{new Date(user.last_login).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })}</td>\r\n                                      </tr>\r\n                                    ))\r\n                                  ) : (\r\n                                    <tr>\r\n                                      <td colSpan=\"3\" className=\"text-center\">No recent user activity</td>\r\n                                    </tr>\r\n                                  )}\r\n                                </tbody>\r\n                              </table>\r\n                            </div>\r\n                          </Card.Body>\r\n                        </Card>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n                </Tab>\r\n              </Tabs>\r\n            </Card.Body>\r\n          </Card>\r\n\r\n          {/* Export buttons */}\r\n          <div className=\"d-flex justify-content-end mb-2\">\r\n            <Button variant=\"outline-danger\" size=\"sm\" className=\"me-2\" onClick={handleDownloadPDF}>\r\n              <i className=\"fas fa-file-pdf me-1\"></i>Download PDF\r\n            </Button>\r\n            <Button variant=\"outline-success\" size=\"sm\" onClick={handleDownloadExcel}>\r\n              <i className=\"fas fa-file-excel me-1\"></i>Download Excel\r\n            </Button>\r\n          </div>\r\n        </>\r\n      )}\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default Dashboard; "], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,QAAQ,iBAAiB;AACpF,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAOC,SAAS,MAAM,yBAAyB;AAC/C,OAAO,iBAAiB;AACxB,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAO,KAAKC,IAAI,MAAM,MAAM;AAC5B,SAASC,MAAM,QAAQ,YAAY;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAUA,MAAMC,qBAAqB,GAAGA,CAACC,SAAS,EAAEC,SAAS,GAAG,UAAU,KAAK;EACnEC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;IAAEH,SAAS;IAAEC;EAAU,CAAC,CAAC;EAEtE,IAAI,CAACD,SAAS,EAAE;IACdE,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,YAAY,GAAG,GAAGH,SAAS,iBAAiB;EAClD,IAAID,SAAS,CAACI,YAAY,CAAC,EAAE;IAC3BF,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,YAAY,EAAEJ,SAAS,CAACI,YAAY,CAAC,CAAC;IAC3E;IACA,MAAMC,QAAQ,GAAGL,SAAS,CAACI,YAAY,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;IACnD,MAAMC,WAAW,GAAGF,QAAQ,CAACG,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACrE,IAAIH,WAAW,KAAK,CAAC,CAAC,IAAIA,WAAW,GAAG,CAAC,GAAGF,QAAQ,CAACM,MAAM,EAAE;MAC3D,MAAMC,KAAK,GAAGP,QAAQ,CAACQ,KAAK,CAACN,WAAW,GAAG,CAAC,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC;MACvD,MAAMC,QAAQ,GAAG,8BAA8BC,kBAAkB,CAACJ,KAAK,CAAC,EAAE;MAC1EV,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEY,QAAQ,CAAC;MAC3D,OAAOA,QAAQ;IACjB;EACF;;EAEA;EACA,MAAME,UAAU,GAAG,GAAGhB,SAAS,eAAe;EAC9C,IAAID,SAAS,CAACiB,UAAU,CAAC,EAAE;IACzBf,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEc,UAAU,EAAEjB,SAAS,CAACiB,UAAU,CAAC,CAAC;;IAErE;IACA,MAAML,KAAK,GAAGZ,SAAS,CAACiB,UAAU,CAAC;IACnC,MAAMC,UAAU,GAAGN,KAAK,CAACN,KAAK,CAAC,GAAG,CAAC,CAACa,GAAG,CAACV,IAAI,IAAIO,kBAAkB,CAACP,IAAI,CAAC,CAAC,CAACK,IAAI,CAAC,GAAG,CAAC;IACnF,MAAMM,GAAG,GAAG,8BAA8BF,UAAU,EAAE;IAEtDhB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEiB,GAAG,CAAC;IACpDlB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAES,KAAK,CAAC;IACtCV,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEe,UAAU,CAAC;IAE1C,OAAOE,GAAG;EACZ;;EAEA;EACA,MAAMC,aAAa,GAAG,GAAGpB,SAAS,WAAW;EAC7C,IAAID,SAAS,CAACqB,aAAa,CAAC,EAAE;IAC5BnB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkB,aAAa,EAAErB,SAAS,CAACqB,aAAa,CAAC,CAAC;IAC3E,MAAMD,GAAG,GAAG,2BAA2BpB,SAAS,CAACqB,aAAa,CAAC,EAAE;IACjEnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiB,GAAG,CAAC;IACzC,OAAOA,GAAG;EACZ;;EAEA;EACAlB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEF,SAAS,EAAED,SAAS,CAAC;EAChE,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMsB,oBAAoB,GAAGA,CAAC;EAAEtB,SAAS;EAAEC,SAAS,GAAG,UAAU;EAAEsB,GAAG;EAAEC,SAAS;EAAEC,KAAK;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EACtG,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,CAAC,CAAC;EAE3DC,SAAS,CAAC,MAAM;IACd;IACAqD,WAAW,CAAC,KAAK,CAAC;IAClBE,mBAAmB,CAAC,CAAC,CAAC;;IAEtB;IACA,MAAMC,QAAQ,GAAGnC,qBAAqB,CAACC,SAAS,EAAEC,SAAS,CAAC;;IAE5D;IACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;MACzCF,SAAS;MACTD,SAAS;MACTmC,YAAY,EAAED,QAAQ;MACtBjB,UAAU,EAAE,GAAGhB,SAAS,eAAe;MACvCmC,UAAU,EAAEpC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,GAAGC,SAAS,eAAe,CAAC;MACpDG,YAAY,EAAE,GAAGH,SAAS,iBAAiB;MAC3CoC,YAAY,EAAErC,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG,GAAGC,SAAS,iBAAiB;IACzD,CAAC,CAAC;IAEF4B,kBAAkB,CAACK,QAAQ,CAAC;EAC9B,CAAC,EAAE,CAAClC,SAAS,EAAEC,SAAS,CAAC,CAAC;EAE1B,MAAMqC,gBAAgB,GAAIC,KAAK,IAAK;IAAA,IAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA;IAClC1C,OAAO,CAAC2C,KAAK,CAAC,sBAAsB,EAAE;MACpC5C,SAAS;MACT2B,eAAe;MACfI,gBAAgB;MAChBa,KAAK,EAAEN,KAAK,aAALA,KAAK,wBAAAC,aAAA,GAALD,KAAK,CAAEO,MAAM,cAAAN,aAAA,uBAAbA,aAAA,CAAeK,KAAK;MAC3BE,GAAG,EAAER,KAAK,aAALA,KAAK,wBAAAE,cAAA,GAALF,KAAK,CAAEO,MAAM,cAAAL,cAAA,uBAAbA,cAAA,CAAeM,GAAG;MACvBC,YAAY,EAAET,KAAK,aAALA,KAAK,wBAAAG,cAAA,GAALH,KAAK,CAAEO,MAAM,cAAAJ,cAAA,uBAAbA,cAAA,CAAeM,YAAY;MACzCC,aAAa,EAAEV,KAAK,aAALA,KAAK,wBAAAI,cAAA,GAALJ,KAAK,CAAEO,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeM,aAAa;MAC3CC,QAAQ,EAAEX,KAAK,aAALA,KAAK,wBAAAK,cAAA,GAALL,KAAK,CAAEO,MAAM,cAAAF,cAAA,uBAAbA,cAAA,CAAeM;IAC3B,CAAC,CAAC;;IAEF;IACA,IAAItB,eAAe,EAAE;MACnBuB,KAAK,CAACvB,eAAe,EAAE;QAAEwB,MAAM,EAAE;MAAO,CAAC,CAAC,CACvCC,IAAI,CAACC,QAAQ,IAAI;QAChBpD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;UAChCiB,GAAG,EAAEQ,eAAe;UACpB2B,MAAM,EAAED,QAAQ,CAACC,MAAM;UACvBC,UAAU,EAAEF,QAAQ,CAACE,UAAU;UAC/BC,OAAO,EAAEC,MAAM,CAACC,WAAW,CAACL,QAAQ,CAACG,OAAO,CAACG,OAAO,CAAC,CAAC;QACxD,CAAC,CAAC;MACJ,CAAC,CAAC,CACDC,KAAK,CAACC,UAAU,IAAI;QACnB5D,OAAO,CAAC2C,KAAK,CAAC,2BAA2B,EAAE;UACzCzB,GAAG,EAAEQ,eAAe;UACpBiB,KAAK,EAAEiB,UAAU,CAACC;QACpB,CAAC,CAAC;MACJ,CAAC,CAAC;IACN;IAEA,IAAI/B,gBAAgB,KAAK,CAAC,EAAE;MAC1B;MACA,MAAMgC,WAAW,GAAGC,mBAAmB,CAACjE,SAAS,EAAEC,SAAS,CAAC;MAC7DC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6D,WAAW,CAAC;MAEnD,IAAIA,WAAW,IAAIA,WAAW,KAAKpC,eAAe,EAAE;QAClDC,kBAAkB,CAACmC,WAAW,CAAC;QAC/B/B,mBAAmB,CAAC,CAAC,CAAC;QACtB;MACF;IACF;;IAEA;IACA/B,OAAO,CAAC2C,KAAK,CAAC,0CAA0C,EAAE5C,SAAS,CAAC;IACpE8B,WAAW,CAAC,IAAI,CAAC;IACjB,IAAIL,OAAO,EAAEA,OAAO,CAAC,CAAC;EACxB,CAAC;EAED,MAAMuC,mBAAmB,GAAGA,CAACjE,SAAS,EAAEC,SAAS,KAAK;IACpDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,SAAS,EAAED,SAAS,CAAC;;IAEjE;IACA,MAAMI,YAAY,GAAG,GAAGH,SAAS,iBAAiB;IAClD,IAAID,SAAS,CAACI,YAAY,CAAC,EAAE;MAC3BF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEH,SAAS,CAACI,YAAY,CAAC,CAAC;MAChE,OAAOJ,SAAS,CAACI,YAAY,CAAC;IAChC;;IAEA;IACA,MAAMiB,aAAa,GAAG,GAAGpB,SAAS,WAAW;IAC7C,IAAID,SAAS,CAACqB,aAAa,CAAC,EAAE;MAC5BnB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEH,SAAS,CAACqB,aAAa,CAAC,CAAC;MAC9D,OAAO,2BAA2BrB,SAAS,CAACqB,aAAa,CAAC,EAAE;IAC9D;;IAEA;IACA,MAAMJ,UAAU,GAAG,GAAGhB,SAAS,eAAe;IAC9C,IAAID,SAAS,CAACiB,UAAU,CAAC,EAAE;MACzBf,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;MACtD,MAAMS,KAAK,GAAGZ,SAAS,CAACiB,UAAU,CAAC;MACnC,MAAMiD,cAAc,GAAG,8BAA8BlD,kBAAkB,CAACJ,KAAK,CAAC,EAAE;MAChFV,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE+D,cAAc,CAAC;MACxD,OAAOA,cAAc;IACvB;IAEAhE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,OAAO,IAAI;EACb,CAAC;EAED,IAAI2B,QAAQ,IAAI,CAACF,eAAe,EAAE;IAChC,oBACEhC,OAAA;MAAK4B,SAAS,EAAE,+DAA+DA,SAAS,EAAG;MAACC,KAAK,EAAEA,KAAM;MAAA0C,QAAA,eACvGvE,OAAA;QAAK4B,SAAS,EAAC,aAAa;QAAA2C,QAAA,gBAC1BvE,OAAA;UAAG4B,SAAS,EAAC;QAA+B;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjD3E,OAAA;UAAAuE,QAAA,EAAK;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE3E,OAAA;IAAK4B,SAAS,EAAC,mBAAmB;IAAA2C,QAAA,eAChCvE,OAAA;MACEmD,GAAG,EAAEnB,eAAgB;MACrBL,GAAG,EAAEA,GAAI;MACTC,SAAS,EAAEA,SAAU;MACrBC,KAAK,EAAEA,KAAM;MACbC,OAAO,EAAEY,gBAAiB;MAC1BkC,OAAO,EAAC,MAAM;MACdC,MAAM,EAAEA,CAAA,KAAM;QACZvE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEyB,eAAe,CAAC;QAC5DG,WAAW,CAAC,KAAK,CAAC;MACpB;IAAE;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAA5C,EAAA,CAxIML,oBAAoB;AAAAoD,EAAA,GAApBpD,oBAAoB;AAyI1B,MAAMqD,SAAS,GAAGA,CAAC;EAAEC,MAAM;EAAEC,UAAU;EAAEC;AAAY,CAAC,KAAK;EAAAC,GAAA;EACzD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGxG,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,IAAI,CAACmG,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,MAAMM,UAAU,GAAIC,YAAY,IAAK;IACnCF,aAAa,CAACE,YAAY,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGR,MAAM,CAACS,gBAAgB,IAAIT,MAAM,CAACS,gBAAgB,CAAC1E,MAAM,GAAG,CAAC;EACnF,MAAM2E,eAAe,GAAGV,MAAM,CAACS,gBAAgB,IAAI,EAAE;EAErD,oBACEzF,OAAA,CAACd,GAAG;IAACyG,EAAE,EAAE,CAAE;IAAC/D,SAAS,EAAC,MAAM;IAAA2C,QAAA,eAC1BvE,OAAA,CAACb,IAAI;MAACyC,SAAS,EAAE,mBAAmB4D,aAAa,GAAG,gBAAgB,GAAG,EAAE,EAAG;MAAAjB,QAAA,gBAC1EvE,OAAA,CAACb,IAAI,CAACyG,MAAM;QAAChE,SAAS,EAAE4D,aAAa,GAAG,0BAA0B,GAAG,EAAG;QAAAjB,QAAA,gBACtEvE,OAAA;UAAK4B,SAAS,EAAC,mDAAmD;UAAA2C,QAAA,gBAChEvE,OAAA;YAAI4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,EACjBU,UAAU,KAAK,QAAQ,GAAG,GAAGD,MAAM,CAACa,UAAU,IAAI,OAAO,KAAKb,MAAM,CAACc,QAAQ,IAAI,KAAK,EAAE,GACxFb,UAAU,KAAK,OAAO,GAAG,GAAGD,MAAM,CAACe,SAAS,IAAI,MAAM,KAAKf,MAAM,CAACgB,OAAO,IAAI,KAAK,EAAE,GACpF,YAAYhB,MAAM,CAACiB,UAAU,IAAI,KAAK;UAAE;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,EACJa,aAAa,iBACZxF,OAAA;YAAO4B,SAAS,EAAC,sBAAsB;YAAA2C,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EACLa,aAAa,iBACZxF,OAAA;UAAK4B,SAAS,EAAC,MAAM;UAAA2C,QAAA,eACnBvE,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAA2C,QAAA,GAAC,iBACb,EAACmB,eAAe,CAACQ,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKlB,UAAU,CAAC,CAAC/D,IAAI,CAAC,IAAI,CAAC;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;QAAA7B,QAAA,gBACRvE,OAAA;UAAK4B,SAAS,EAAC,kBAAkB;UAAA2C,QAAA,eAC/BvE,OAAA,CAAC0B,oBAAoB;YACnBtB,SAAS,EAAE4E,MAAO;YAClB3E,SAAS,EAAE+E,UAAU,GAAG,UAAU,GAAG,WAAY;YACjDzD,GAAG,EAAE,GAAGsD,UAAU,KAAK,QAAQ,GAAG,OAAO,GAAGA,UAAU,KAAK,OAAO,GAAG,MAAM,GAAG,SAAS,IAAID,MAAM,CAACE,WAAW,CAAC,EAAG;YACjHtD,SAAS,EAAC,uBAAuB;YACjCC,KAAK,EAAE;cAAEwE,SAAS,EAAE;YAAQ,CAAE;YAC9BvE,OAAO,EAAEA,CAAA,KAAM;cACbxB,OAAO,CAACgG,IAAI,CAAC,kBAAkBlB,UAAU,GAAG,UAAU,GAAG,WAAW,cAAcH,UAAU,IAAID,MAAM,CAACE,WAAW,CAAC,EAAE,CAAC;YACxH;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN3E,OAAA;UAAK4B,SAAS,EAAC,OAAO;UAAA2C,QAAA,GACnBU,UAAU,KAAK,UAAU,iBACxBjF,OAAA,CAAAE,SAAA;YAAAqE,QAAA,gBACEvE,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAACuB,QAAQ,GAAGvB,MAAM,CAACuB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,EAAC,SAAI;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzG3E,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAACyB,QAAQ,GAAGzB,MAAM,CAACyB,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,EAAC,KAAG;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzG3E,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAAC0B,MAAM,GAAG1B,MAAM,CAAC0B,MAAM,CAACF,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,eACnG,CACH,EACAM,UAAU,KAAK,QAAQ,iBACtBjF,OAAA,CAAAE,SAAA;YAAAqE,QAAA,gBACEvE,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAACa,UAAU,IAAI,KAAK;YAAA;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3E3E,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAACuB,QAAQ,GAAGvB,MAAM,CAACuB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,EAAC,SAAI;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzG3E,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAAC2B,UAAU,IAAI,KAAK;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,eAC5E,CACH,EACAM,UAAU,KAAK,OAAO,iBACrBjF,OAAA,CAAAE,SAAA;YAAAqE,QAAA,gBACEvE,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAAC4B,SAAS,IAAI,KAAK;YAAA;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1E3E,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAAC6B,QAAQ,GAAG7B,MAAM,CAAC6B,QAAQ,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,EAAC,IAAE;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACzG3E,OAAA;cAAG4B,SAAS,EAAC,MAAM;cAAA2C,QAAA,gBAACvE,OAAA;gBAAAuE,QAAA,EAAQ;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAACe,SAAS,IAAI,KAAK;YAAA;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,eAC/E,CACH,eACD3E,OAAA;YAAG4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,gBAACvE,OAAA;cAAAuE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAAC8B,QAAQ,IAAI,SAAS;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpF3E,OAAA;YAAG4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,gBAACvE,OAAA;cAAAuE,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACK,MAAM,CAAC+B,SAAS,GAAG,IAAIC,IAAI,CAAChC,MAAM,CAAC+B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;cAAEC,QAAQ,EAAE;YAAe,CAAC,CAAC,GAAG,KAAK;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjK3E,OAAA;YAAK4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,gBACnBvE,OAAA,CAACT,MAAM;cACL4H,OAAO,EAAE/B,UAAU,GAAG,SAAS,GAAG,iBAAkB;cACpDgC,IAAI,EAAC,IAAI;cACTxF,SAAS,EAAC,MAAM;cAChByF,OAAO,EAAEA,CAAA,KAAM/B,UAAU,CAAC,IAAI,CAAE;cAAAf,QAAA,EACjC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3E,OAAA,CAACT,MAAM;cACL4H,OAAO,EAAE,CAAC/B,UAAU,GAAG,SAAS,GAAG,iBAAkB;cACrDgC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAM/B,UAAU,CAAC,KAAK,CAAE;cAAAf,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC,GA/EyB,GAAGM,UAAU,IAAID,MAAM,CAACE,WAAW,CAAC,IAAIF,MAAM,CAACsC,QAAQ,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE;IAAAhD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAgFvG,CAAC;AAEV,CAAC;;AAED;AAAAQ,GAAA,CArGMJ,SAAS;AAAA0C,GAAA,GAAT1C,SAAS;AAsGf,MAAM2C,SAAS,GAAGA,CAAC;EAAEC;AAAM,CAAC,KAAK;EAC/B;EACA,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,MAAMC,cAAc,GAAG,MAAOC,SAAS,IAAK;IAC1C,IAAI;MACF;MACA,MAAMC,OAAO,GAAGH,KAAK,CAACI,GAAG,IAAIJ,KAAK,CAACK,QAAQ;MAC3C,MAAMC,WAAW,GAAG,8BAA8BH,OAAO,IAAID,SAAS,EAAE;MAExEvH,OAAO,CAACC,GAAG,CAAC,eAAesH,SAAS,2BAA2BC,OAAO,EAAE,CAAC;;MAEzE;MACA,MAAMpE,QAAQ,GAAG,MAAMH,KAAK,CAAC0E,WAAW,EAAE;QACxCzE,MAAM,EAAE,KAAK;QACbK,OAAO,EAAE;UACP,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACwE,EAAE,EAAE;QAChB,MAAMC,SAAS,GAAG,MAAMzE,QAAQ,CAAC0E,IAAI,CAAC,CAAC,CAACnE,KAAK,CAAC,OAAO;UAAEE,OAAO,EAAE;QAAgB,CAAC,CAAC,CAAC;QACnF,MAAM,IAAIkE,KAAK,CAACF,SAAS,CAAChE,OAAO,IAAI,QAAQT,QAAQ,CAACC,MAAM,KAAKD,QAAQ,CAACE,UAAU,EAAE,CAAC;MACzF;;MAEA;MACA,MAAM0E,SAAS,GAAG,MAAM5E,QAAQ,CAAC6E,IAAI,CAAC,CAAC;MACvCjI,OAAO,CAACC,GAAG,CAAC,gBAAgBsH,SAAS,uBAAuBS,SAAS,CAAClB,IAAI,iBAAiBkB,SAAS,CAACE,IAAI,EAAE,CAAC;;MAE5G;MACA,MAAMC,OAAO,GAAGC,GAAG,CAACC,eAAe,CAACL,SAAS,CAAC;;MAE9C;MACA,MAAMM,QAAQ,GAAG,GAAGf,SAAS,UAAU,CAACF,KAAK,CAACK,QAAQ,IAAIF,OAAO,EAAEe,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM;;MAExF;MACA,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGR,OAAO;MACnBK,IAAI,CAACI,QAAQ,GAAGN,QAAQ;MACxBE,IAAI,CAACjH,KAAK,CAACsH,OAAO,GAAG,MAAM;MAC3BJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;MAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;MACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;;MAE/B;MACAU,UAAU,CAAC,MAAM;QACfd,GAAG,CAACe,eAAe,CAAChB,OAAO,CAAC;QAC5BnI,OAAO,CAACC,GAAG,CAAC,8BAA8BsH,SAAS,QAAQ,CAAC;MAC9D,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAO5E,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,uBAAuB4E,SAAS,SAAS,EAAE5E,KAAK,CAAC;MAC/DyG,KAAK,CAAC,qBAAqB7B,SAAS,WAAW5E,KAAK,CAACkB,OAAO,EAAE,CAAC;IACjE;EACF,CAAC;EAED,MAAMwF,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MACF,MAAMC,YAAY,GAAGD,MAAM,CAACE,WAAW,CAAC,CAAC;MACzC,MAAMhC,OAAO,GAAGH,KAAK,CAACI,GAAG,IAAIJ,KAAK,CAACK,QAAQ;;MAE3C;MACA,MAAMtE,QAAQ,GAAG,MAAMH,KAAK,CAAC,iDAAiDsG,YAAY,aAAa/B,OAAO,EAAE,EAAE;QAChHtE,MAAM,EAAE,KAAK;QACbK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACH,QAAQ,CAACwE,EAAE,EAAE;QAChB,MAAM,IAAIG,KAAK,CAAC,kBAAkB3E,QAAQ,CAACE,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMmG,IAAI,GAAG,MAAMrG,QAAQ,CAAC0E,IAAI,CAAC,CAAC;MAElC,IAAIyB,YAAY,KAAK,KAAK,EAAE;QAC1B;QACA,IAAIE,IAAI,CAACC,QAAQ,EAAE;UACjB,MAAMC,cAAc,GAAGC,IAAI,CAACH,IAAI,CAACC,QAAQ,CAAC;UAC1C,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAAClJ,MAAM,CAAC;UACpD,KAAK,IAAIsJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,cAAc,CAAClJ,MAAM,EAAEsJ,CAAC,EAAE,EAAE;YAC9CF,WAAW,CAACE,CAAC,CAAC,GAAGJ,cAAc,CAACK,UAAU,CAACD,CAAC,CAAC;UAC/C;UACA,MAAME,SAAS,GAAG,IAAIC,UAAU,CAACL,WAAW,CAAC;UAC7C,MAAM5B,IAAI,GAAG,IAAIkC,IAAI,CAAC,CAACF,SAAS,CAAC,EAAE;YAAE/B,IAAI,EAAE;UAAkB,CAAC,CAAC;UAE/D,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxC,MAAMxH,GAAG,GAAGkH,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UACrCO,IAAI,CAAC4B,YAAY,CAAC,MAAM,EAAElJ,GAAG,CAAC;UAC9BsH,IAAI,CAAC4B,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC/C,KAAK,CAACK,QAAQ,IAAIF,OAAO,EAAEe,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,sBAAsB,CAAC;UACzGC,IAAI,CAACjH,KAAK,CAAC8I,UAAU,GAAG,QAAQ;UAChC5B,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;UAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;UACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;UAC/BJ,GAAG,CAACe,eAAe,CAACjI,GAAG,CAAC;QAC1B;MACF,CAAC,MAAM,IAAIqI,YAAY,KAAK,KAAK,EAAE;QACjC;QACA,IAAIE,IAAI,CAACa,QAAQ,EAAE;UACjB,MAAMC,UAAU,GAAGd,IAAI,CAACa,QAAQ,CAACrJ,GAAG,CAACuJ,GAAG,IAAIA,GAAG,CAAC5J,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;UACrE,MAAMqH,IAAI,GAAG,IAAIkC,IAAI,CAAC,CAACI,UAAU,CAAC,EAAE;YAAErC,IAAI,EAAE;UAA0B,CAAC,CAAC;UAExE,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;UACxC,MAAMxH,GAAG,GAAGkH,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;UACrCO,IAAI,CAAC4B,YAAY,CAAC,MAAM,EAAElJ,GAAG,CAAC;UAC9BsH,IAAI,CAAC4B,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC/C,KAAK,CAACK,QAAQ,IAAIF,OAAO,EAAEe,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,sBAAsB,CAAC;UACzGC,IAAI,CAACjH,KAAK,CAAC8I,UAAU,GAAG,QAAQ;UAChC5B,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI,CAAC;UAC/BA,IAAI,CAACQ,KAAK,CAAC,CAAC;UACZP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI,CAAC;UAC/BJ,GAAG,CAACe,eAAe,CAACjI,GAAG,CAAC;QAC1B;MACF;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACd3C,OAAO,CAAC2C,KAAK,CAAC,sBAAsB2G,MAAM,GAAG,EAAE3G,KAAK,CAAC;MACrDyG,KAAK,CAAC,sBAAsBE,MAAM,KAAK3G,KAAK,CAACkB,OAAO,EAAE,CAAC;IACzD;EACF,CAAC;EAED,MAAM4G,eAAe,GAAGpD,KAAK,CAACqD,gBAAgB,IAAI,CAAC,CAAC;EACpD,MAAMC,eAAe,GAAGF,eAAe,CAACG,KAAK,IAAI,CAAC;EAElD,oBACElL,OAAA,CAACd,GAAG;IAACyG,EAAE,EAAE,CAAE;IAAC/D,SAAS,EAAC,MAAM;IAAA2C,QAAA,eAC1BvE,OAAA,CAACb,IAAI;MAACyC,SAAS,EAAC,iBAAiB;MAAA2C,QAAA,gBAC/BvE,OAAA,CAACb,IAAI,CAACyG,MAAM;QAAChE,SAAS,EAAC,uBAAuB;QAAA2C,QAAA,gBAC5CvE,OAAA;UAAK4B,SAAS,EAAC,mDAAmD;UAAA2C,QAAA,gBAChEvE,OAAA;YAAI4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,GAAC,SACZ,EAACoD,KAAK,CAACK,QAAQ,CAACa,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAC,KACzC;UAAA;YAAArE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3E,OAAA;YAAO4B,SAAS,EAAC,mBAAmB;YAAA2C,QAAA,EAAC;UAErC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN3E,OAAA;UAAK4B,SAAS,EAAC,MAAM;UAAA2C,QAAA,eACnBvE,OAAA;YAAO4B,SAAS,EAAC,YAAY;YAAA2C,QAAA,GAAC,UACpB,EAACoD,KAAK,CAACwD,UAAU,GAAGxD,KAAK,CAACwD,UAAU,CAACjK,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK;UAAA;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;QAAA7B,QAAA,gBACRvE,OAAA;UAAK4B,SAAS,EAAC,kBAAkB;UAAA2C,QAAA,EAC9BoD,KAAK,CAACyD,oBAAoB,gBACzBpL,OAAA;YACEmD,GAAG,EAAE,0BAA0BwE,KAAK,CAACyD,oBAAoB,EAAG;YAC5DzJ,GAAG,EAAC,iBAAiB;YACrBC,SAAS,EAAC,uBAAuB;YACjCC,KAAK,EAAE;cAAEwE,SAAS,EAAE;YAAQ,CAAE;YAC9BvE,OAAO,EAAGuJ,CAAC,IAAK;cACd/K,OAAO,CAACgG,IAAI,CAAC,iDAAiDqB,KAAK,CAACK,QAAQ,EAAE,CAAC;cAC/EqD,CAAC,CAACnI,MAAM,CAACrB,KAAK,CAACsH,OAAO,GAAG,MAAM;YACjC;UAAE;YAAA3E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEF3E,OAAA;YAAK4B,SAAS,EAAC,yDAAyD;YAACC,KAAK,EAAE;cAAEyJ,MAAM,EAAE,OAAO;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAhH,QAAA,eAC9HvE,OAAA;cAAM4B,SAAS,EAAC,YAAY;cAAA2C,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN3E,OAAA;UAAK4B,SAAS,EAAC,OAAO;UAAA2C,QAAA,gBACpBvE,OAAA;YAAG4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,eAACvE,OAAA;cAAAuE,QAAA,EAAQ;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpD3E,OAAA;YAAI4B,SAAS,EAAC,MAAM;YAACC,KAAK,EAAE;cAAE2J,WAAW,EAAE;YAAO,CAAE;YAAAjH,QAAA,gBAClDvE,OAAA;cAAAuE,QAAA,GAAI,YAAU,EAACwG,eAAe,CAACU,QAAQ,IAAI,CAAC;YAAA;cAAAjH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClD3E,OAAA;cAAAuE,QAAA,GAAI,UAAQ,EAACwG,eAAe,CAACW,MAAM,IAAI,CAAC;YAAA;cAAAlH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC9C3E,OAAA;cAAAuE,QAAA,GAAI,SAAO,EAACwG,eAAe,CAACY,KAAK,IAAI,CAAC;YAAA;cAAAnH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eACL3E,OAAA;YAAG4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,gBAACvE,OAAA;cAAAuE,QAAA,EAAQ;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACsG,eAAe;UAAA;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5E3E,OAAA;YAAG4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,gBAACvE,OAAA;cAAAuE,QAAA,EAAQ;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACgD,KAAK,CAACb,QAAQ,IAAI,SAAS;UAAA;YAAAtC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF3E,OAAA;YAAG4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,gBAACvE,OAAA;cAAAuE,QAAA,EAAQ;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACgD,KAAK,CAACZ,SAAS,GAAG,IAAIC,IAAI,CAACW,KAAK,CAACZ,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;cAAEC,QAAQ,EAAE;YAAe,CAAC,CAAC,GAAG,KAAK;UAAA;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAG/J3E,OAAA;YAAK4B,SAAS,EAAC,WAAW;YAAA2C,QAAA,gBACxBvE,OAAA,CAACT,MAAM;cACL4H,OAAO,EAAC,SAAS;cACjBC,IAAI,EAAC,IAAI;cACTxF,SAAS,EAAC,WAAW;cACrByF,OAAO,EAAEA,CAAA,KAAMO,cAAc,CAAC,UAAU,CAAE;cAC1CgE,QAAQ,EAAE,CAACjE,KAAK,CAACkE,kBAAmB;cAAAtH,QAAA,EACrC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3E,OAAA,CAACT,MAAM;cACL4H,OAAO,EAAC,SAAS;cACjBC,IAAI,EAAC,IAAI;cACTxF,SAAS,EAAC,WAAW;cACrByF,OAAO,EAAEA,CAAA,KAAMO,cAAc,CAAC,WAAW,CAAE;cAC3CgE,QAAQ,EAAE,CAACjE,KAAK,CAACmE,mBAAoB;cAAAvH,QAAA,EACtC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN3E,OAAA;YAAK4B,SAAS,EAAC,MAAM;YAAA2C,QAAA,gBACnBvE,OAAA,CAACT,MAAM;cACL4H,OAAO,EAAC,mBAAmB;cAC3BC,IAAI,EAAC,IAAI;cACTxF,SAAS,EAAC,MAAM;cAChByF,OAAO,EAAEA,CAAA,KAAMsC,YAAY,CAAC,KAAK,CAAE;cAAApF,QAAA,EACpC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3E,OAAA,CAACT,MAAM;cACL4H,OAAO,EAAC,mBAAmB;cAC3BC,IAAI,EAAC,IAAI;cACTC,OAAO,EAAEA,CAAA,KAAMsC,YAAY,CAAC,KAAK,CAAE;cAAApF,QAAA,EACpC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC,GAzFyB,SAASgD,KAAK,CAACK,QAAQ,EAAE;IAAAxD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA0FtD,CAAC;AAEV,CAAC;AAACoH,GAAA,GAzNIrE,SAAS;AA2Nf,SAASsE,SAASA,CAAC;EAAEC;AAAK,CAAC,EAAE;EAAAC,GAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EAC3B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7N,QAAQ,CAAC;IAC3C8N,gBAAgB,EAAE,CAAC;IACnBC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnO,QAAQ,CAAC;IAC3CoO,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvO,QAAQ,CAAC;IAC/CwO,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG3O,QAAQ,CAAC;IACjD4M,QAAQ,EAAE;MACRgC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,CAAC,CAAC;MACXC,UAAU,EAAE,CAAC;MACbC,MAAM,EAAE;IACV,CAAC;IACDlC,MAAM,EAAE;MACN+B,KAAK,EAAE,CAAC;MACRI,OAAO,EAAE,CAAC,CAAC;MACXH,OAAO,EAAE,CAAC,CAAC;MACXE,MAAM,EAAE;IACV,CAAC;IACDjC,KAAK,EAAE;MACL8B,KAAK,EAAE,CAAC;MACRK,YAAY,EAAE,CAAC,CAAC;MAChBF,MAAM,EAAE;IACV,CAAC;IACDG,KAAK,EAAE;MACLN,KAAK,EAAE,CAAC;MACRO,OAAO,EAAE,CAAC,CAAC;MACXJ,MAAM,EAAE;IACV,CAAC;IACDK,MAAM,EAAE;MACNR,KAAK,EAAE,CAAC;MACRG,MAAM,EAAE;IACV;EACF,CAAC,CAAC;EACF,MAAM,CAAChJ,OAAO,EAAEsJ,UAAU,CAAC,GAAGrP,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoE,KAAK,EAAEkL,QAAQ,CAAC,GAAGtP,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAM,CAACuP,SAAS,EAAEC,YAAY,CAAC,GAAGxP,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyP,OAAO,EAAEC,UAAU,CAAC,GAAG1P,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2P,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5P,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAAC6P,SAAS,EAAEC,YAAY,CAAC,GAAG9P,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+P,YAAY,EAAEC,eAAe,CAAC,GAAGhQ,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiQ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlQ,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACmQ,aAAa,EAAEC,gBAAgB,CAAC,GAAGpQ,QAAQ,CAAC;IACjD4M,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM,CAACuD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtQ,QAAQ,CAAC;IAC/DwO,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAGxQ,QAAQ,CAAC,WAAW,CAAC;;EAEvD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwQ,WAAW,GAAG,IAAItI,IAAI,CAAC,CAAC;IAC9B,MAAMuI,QAAQ,GAAG,IAAIvI,IAAI,CAAC,CAAC;IAC3BuI,QAAQ,CAACC,OAAO,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE7C,MAAMC,gBAAgB,GAAGJ,WAAW,CAACK,WAAW,CAAC,CAAC,CAACjP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChE,MAAMkP,kBAAkB,GAAGL,QAAQ,CAACI,WAAW,CAAC,CAAC,CAACjP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAE/D6N,UAAU,CAACmB,gBAAgB,CAAC;IAC5BrB,YAAY,CAACuB,kBAAkB,CAAC;IAChCC,SAAS,CAAC;MAAEzB,SAAS,EAAEwB,kBAAkB;MAAEtB,OAAO,EAAEoB;IAAiB,CAAC,CAAC;EACzE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,SAAS,GAAG,MAAAA,CAAOC,OAAO,GAAG,CAAC,CAAC,KAAK;IACxC,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM6B,MAAM,GAAG,CAAC,CAAC;MACjB,IAAID,OAAO,CAAC1B,SAAS,EAAE2B,MAAM,CAACC,UAAU,GAAGF,OAAO,CAAC1B,SAAS;MAC5D,IAAI0B,OAAO,CAACxB,OAAO,EAAEyB,MAAM,CAACE,QAAQ,GAAGH,OAAO,CAACxB,OAAO;MACtD,IAAIwB,OAAO,CAAChJ,QAAQ,EAAEiJ,MAAM,CAACjJ,QAAQ,GAAGgJ,OAAO,CAAChJ,QAAQ;MACxD,IAAImF,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiE,IAAI,EAAEH,MAAM,CAACI,SAAS,GAAGlE,IAAI,CAACiE,IAAI;;MAE5C;MACA,MAAME,aAAa,GAAG,MAAMrR,KAAK,CAACsR,GAAG,CAAC,2BAA2B,EAAE;QAAEN;MAAO,CAAC,CAAC;MAC9E,IAAIK,aAAa,CAACrG,IAAI,CAACuG,OAAO,EAAE;QAC9B5D,aAAa,CAAC;UACZC,gBAAgB,EAAEyD,aAAa,CAACrG,IAAI,CAACA,IAAI,CAACwG,cAAc,CAAC9E,QAAQ;UACjEmB,cAAc,EAAEwD,aAAa,CAACrG,IAAI,CAACA,IAAI,CAACwG,cAAc,CAAC7E,MAAM;UAC7DmB,aAAa,EAAEuD,aAAa,CAACrG,IAAI,CAACA,IAAI,CAACwG,cAAc,CAAC5E,KAAK;UAC3DmB,UAAU,EAAEL,UAAU,CAACK,UAAU,CAAC;QACpC,CAAC,CAAC;MACJ;;MAEA;MACA,MAAM0D,cAAc,GAAG,MAAMzR,KAAK,CAACsR,GAAG,CAAC,6BAA6B,EAAE;QAAEN;MAAO,CAAC,CAAC;MACjF/C,aAAa,CAAC;QACZC,IAAI,EAAEuD,cAAc,CAACzG,IAAI,CAACkD,IAAI;QAC9BC,MAAM,EAAEsD,cAAc,CAACzG,IAAI,CAACmD;MAC9B,CAAC,CAAC;;MAEF;MACA,MAAMuD,aAAa,GAAG,MAAM1R,KAAK,CAACsR,GAAG,CAAC,+BAA+B,EAAE;QAAEN;MAAO,CAAC,CAAC;MAClF3C,eAAe,CAAC;QACdC,KAAK,EAAEoD,aAAa,CAAC1G,IAAI,CAACsD,KAAK;QAC/BC,MAAM,EAAEmD,aAAa,CAAC1G,IAAI,CAACuD;MAC7B,CAAC,CAAC;;MAEF;MACA,IAAIoD,iBAAiB;MACrB,IAAI;QACF;QACAA,iBAAiB,GAAG,MAAM3R,KAAK,CAACsR,GAAG,CAAC,2BAA2B,EAAE;UAAEN;QAAO,CAAC,CAAC;QAC5EzP,OAAO,CAACC,GAAG,CAAC,0EAA0E,CAAC;MACzF,CAAC,CAAC,OAAOoQ,aAAa,EAAE;QACtBrQ,OAAO,CAACgG,IAAI,CAAC,8DAA8D,EAAEqK,aAAa,CAACxM,OAAO,CAAC;QACnG;QACAuM,iBAAiB,GAAG,MAAM3R,KAAK,CAACsR,GAAG,CAAC,wBAAwB,EAAE;UAAEN;QAAO,CAAC,CAAC;QACzEzP,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC/C;MAEA,IAAImQ,iBAAiB,CAAC3G,IAAI,CAACuG,OAAO,EAAE;QAClC,MAAM/C,aAAa,GAAGmD,iBAAiB,CAAC3G,IAAI,CAACA,IAAI;;QAEjD;QACA,MAAM6G,gBAAgB,GAAG;UACvBC,WAAW,EAAE,CAAC;UACdC,iBAAiB,EAAE,CAAC;UACpBC,kBAAkB,EAAE,CAAC;UACrBC,iBAAiB,EAAE;YACjBvF,QAAQ,EAAE,CAAC;YACXC,MAAM,EAAE,CAAC;YACTC,KAAK,EAAE;UACT;QACF,CAAC;;QAED;QACA,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACsF,OAAO,CAACC,QAAQ,IAAI;UAClD,MAAMC,cAAc,GAAG5D,aAAa,CAAC2D,QAAQ,CAAC,CAACtD,MAAM,IAAI,EAAE;UAC3DuD,cAAc,CAACF,OAAO,CAACG,IAAI,IAAI;YAC7B,IAAIA,IAAI,CAACC,kBAAkB,EAAE;cAC3BT,gBAAgB,CAACE,iBAAiB,EAAE;YACtC;YACAF,gBAAgB,CAACC,WAAW,EAAE;YAC9BD,gBAAgB,CAACI,iBAAiB,CAACE,QAAQ,CAAC,EAAE;UAChD,CAAC,CAAC;QACJ,CAAC,CAAC;QAEFN,gBAAgB,CAACG,kBAAkB,GAAGH,gBAAgB,CAACC,WAAW,GAAGD,gBAAgB,CAACE,iBAAiB;;QAEvG;QACAvD,aAAa,CAACqD,gBAAgB,GAAGA,gBAAgB;QAEjDpD,gBAAgB,CAACD,aAAa,CAAC;MACjC;;MAEA;MACA,IAAI;QACF,MAAM+D,aAAa,GAAG,MAAMvS,KAAK,CAACsR,GAAG,CAAC,oBAAoB,EAAE;UAAEN;QAAO,CAAC,CAAC;QACvE,IAAIuB,aAAa,CAACvH,IAAI,CAACuG,OAAO,EAAE;UAC9B5D,aAAa,CAAC6E,SAAS,KAAK;YAC1B,GAAGA,SAAS;YACZzE,UAAU,EAAEwE,aAAa,CAACvH,IAAI,CAACyH,WAAW,IAAI;UAChD,CAAC,CAAC,CAAC;;UAEH;UACAhE,gBAAgB,CAACiE,QAAQ,KAAK;YAC5B,GAAGA,QAAQ;YACX1D,KAAK,EAAE;cACLN,KAAK,EAAE6D,aAAa,CAACvH,IAAI,CAACyH,WAAW,IAAI,CAAC;cAC1CxD,OAAO,EAAEsD,aAAa,CAACvH,IAAI,CAAC2H,kBAAkB,IAAI,CAAC,CAAC;cACpD9D,MAAM,EAAE0D,aAAa,CAACvH,IAAI,CAAC4H,YAAY,IAAI;YAC7C;UACF,CAAC,CAAC,CAAC;QACL;MACF,CAAC,CAAC,OAAOC,OAAO,EAAE;QAChBtR,OAAO,CAAC2C,KAAK,CAAC,2BAA2B,EAAE2O,OAAO,CAAC;QACnD;MACF;MAEA1D,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAO2D,GAAG,EAAE;MACZ1D,QAAQ,CAAC,+BAA+B,CAAC;MACzCD,UAAU,CAAC,KAAK,CAAC;MACjB5N,OAAO,CAAC2C,KAAK,CAAC,sBAAsB,EAAE4O,GAAG,CAAC;IAC5C;EACF,CAAC;;EAED;EACA/S,SAAS,CAAC,MAAM;IACd,MAAMgT,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,MAAM/B,MAAM,GAAG,CAAC,CAAC;QACjB,IAAI9D,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEiE,IAAI,EAAEH,MAAM,CAACI,SAAS,GAAGlE,IAAI,CAACiE,IAAI;QAE5C,MAAMxM,QAAQ,GAAG,MAAM3E,KAAK,CAACsR,GAAG,CAAC,gBAAgB,EAAE;UAAEN;QAAO,CAAC,CAAC;QAC9D,IAAIrM,QAAQ,CAACqG,IAAI,CAACuG,OAAO,EAAE;UACzB;UACA3B,YAAY,CAACjL,QAAQ,CAACqG,IAAI,CAACgE,KAAK,CAAC;QACnC;MACF,CAAC,CAAC,OAAO9K,KAAK,EAAE;QACd3C,OAAO,CAAC2C,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C;IACF,CAAC;IAED6O,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAC7F,IAAI,CAAC,CAAC;;EAEV;EACA,MAAM8F,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI3D,SAAS,IAAIE,OAAO,EAAE;MACxBuB,SAAS,CAAC;QACRzB,SAAS;QACTE,OAAO;QACPxH,QAAQ,EAAE8H,YAAY,IAAIoD;MAC5B,CAAC,CAAC;MACFvD,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMwD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAM3C,WAAW,GAAG,IAAItI,IAAI,CAAC,CAAC;IAC9B,MAAMuI,QAAQ,GAAG,IAAIvI,IAAI,CAAC,CAAC;IAC3BuI,QAAQ,CAACC,OAAO,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE7C,MAAMyC,UAAU,GAAG5C,WAAW,CAACK,WAAW,CAAC,CAAC,CAACjP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1D,MAAMyR,YAAY,GAAG5C,QAAQ,CAACI,WAAW,CAAC,CAAC,CAACjP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEzD6N,UAAU,CAAC2D,UAAU,CAAC;IACtB7D,YAAY,CAAC8D,YAAY,CAAC;IAC1BtC,SAAS,CAAC;MACRzB,SAAS,EAAE+D,YAAY;MACvB7D,OAAO,EAAE4D,UAAU;MACnBpL,QAAQ,EAAE8H,YAAY,IAAIoD;IAC5B,CAAC,CAAC;IACFvD,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAM2D,qBAAqB,GAAGA,CAAA,KAAM;IAClCvC,SAAS,CAAC;MACRzB,SAAS;MACTE,OAAO;MACPxH,QAAQ,EAAE8H,YAAY,IAAIoD;IAC5B,CAAC,CAAC;IACFjD,oBAAoB,CAAC,CAAC,CAACH,YAAY,CAAC;EACtC,CAAC;;EAED;EACA,MAAMyD,qBAAqB,GAAGA,CAAA,KAAM;IAClCxD,eAAe,CAAC,EAAE,CAAC;IACnBgB,SAAS,CAAC;MACRzB,SAAS;MACTE;IACF,CAAC,CAAC;IACFS,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMuD,gBAAgB,GAAIjH,CAAC,IAAK;IAC9BwD,eAAe,CAACxD,CAAC,CAACnI,MAAM,CAACqP,KAAK,CAAC;EACjC,CAAC;;EAED;EACAzT,SAAS,CAAC,MAAM;IACd,IAAIqO,YAAY,CAACE,KAAK,CAACtM,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMyR,aAAa,GAAG,EAAE;MACxB,MAAMC,cAAc,GAAG,EAAE;MAEzBtF,YAAY,CAACE,KAAK,CAAC4D,OAAO,CAAC,CAACzI,IAAI,EAAEkK,KAAK,KAAK;QAC1C,IACGlK,IAAI,CAAC1H,QAAQ,CAAC,SAAS,CAAC,IAAIkO,aAAa,CAACvD,QAAQ,IAClDjD,IAAI,CAAC1H,QAAQ,CAAC,OAAO,CAAC,IAAIkO,aAAa,CAACtD,MAAO,IAC/ClD,IAAI,CAAC1H,QAAQ,CAAC,MAAM,CAAC,IAAIkO,aAAa,CAACrD,KAAM,EAC9C;UACA6G,aAAa,CAACG,IAAI,CAACnK,IAAI,CAAC;UACxBiK,cAAc,CAACE,IAAI,CAACxF,YAAY,CAACG,MAAM,CAACoF,KAAK,CAAC,CAAC;QACjD;MACF,CAAC,CAAC;MAEFvD,uBAAuB,CAAC;QACtB9B,KAAK,EAAEmF,aAAa;QACpBlF,MAAM,EAAEmF;MACV,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACtF,YAAY,EAAE6B,aAAa,CAAC,CAAC;;EAEjC;EACA,MAAM4D,wBAAwB,GAAI3N,UAAU,IAAK;IAC/CgK,gBAAgB,CAAC4D,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAAC5N,UAAU,GAAG,CAAC4N,IAAI,CAAC5N,UAAU;IAChC,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAM6N,iBAAiB,GAAGA,CAAA,KAAM;IAAA,IAAAC,oBAAA;IAC9B,MAAMC,GAAG,GAAG,IAAIrT,KAAK,CAAC,CAAC;IACvB,IAAIsT,SAAS,GAAG,EAAE;;IAElB;IACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCH,GAAG,CAACI,IAAI,CAAC,+CAA+C,EAAE,GAAG,EAAEH,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAC9FJ,SAAS,IAAI,EAAE;IAEfD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCH,GAAG,CAACI,IAAI,CAAC,iBAAiB,IAAIpM,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAe,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE+L,SAAS,EAAE;MAAEI,KAAK,EAAE;IAAS,CAAC,CAAC;IAClIJ,SAAS,IAAI,EAAE;;IAEf;IACA,IAAIzE,iBAAiB,EAAE;MACrBwE,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCH,GAAG,CAACI,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC7CA,SAAS,IAAI,CAAC;MACdD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCH,GAAG,CAACI,IAAI,CAAC,SAAS,IAAIpM,IAAI,CAACoH,SAAS,CAAC,CAACkF,kBAAkB,CAAC,CAAC,QAAQ,IAAItM,IAAI,CAACsH,OAAO,CAAC,CAACgF,kBAAkB,CAAC,CAAC,EAAE,EAAE,EAAE,EAAEL,SAAS,CAAC;MAC1HA,SAAS,IAAI,EAAE;IACjB;;IAEA;IACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCH,GAAG,CAACI,IAAI,CAAC,oBAAoB,EAAE,EAAE,EAAEH,SAAS,CAAC;IAC7CA,SAAS,IAAI,EAAE;IAEfD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;IAClCH,GAAG,CAACI,IAAI,CAAC,4BAA4B3G,UAAU,CAACE,gBAAgB,EAAE,EAAE,EAAE,EAAEsG,SAAS,CAAC;IAClFA,SAAS,IAAI,CAAC;IACdD,GAAG,CAACI,IAAI,CAAC,0BAA0B3G,UAAU,CAACG,cAAc,EAAE,EAAE,EAAE,EAAEqG,SAAS,CAAC;IAC9EA,SAAS,IAAI,CAAC;IACdD,GAAG,CAACI,IAAI,CAAC,yBAAyB3G,UAAU,CAACI,aAAa,EAAE,EAAE,EAAE,EAAEoG,SAAS,CAAC;IAC5EA,SAAS,IAAI,CAAC;IACdD,GAAG,CAACI,IAAI,CAAC,gBAAgB3G,UAAU,CAACK,UAAU,EAAE,EAAE,EAAE,EAAEmG,SAAS,CAAC;IAChEA,SAAS,IAAI,EAAE;;IAEf;IACAD,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;IACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCH,GAAG,CAACI,IAAI,CAAC,6BAA6B,EAAE,EAAE,EAAEH,SAAS,CAAC;IACtDA,SAAS,IAAI,EAAE;IAEf,MAAMM,WAAW,GAAG9G,UAAU,CAACE,gBAAgB,GAAGF,UAAU,CAACG,cAAc,GAAGH,UAAU,CAACI,aAAa;IACtG,IAAI0G,WAAW,GAAG,CAAC,EAAE;MACnB,MAAMC,cAAc,GAAG,CAAE/G,UAAU,CAACE,gBAAgB,GAAG4G,WAAW,GAAI,GAAG,EAAE/M,OAAO,CAAC,CAAC,CAAC;MACrF,MAAMiN,YAAY,GAAG,CAAEhH,UAAU,CAACG,cAAc,GAAG2G,WAAW,GAAI,GAAG,EAAE/M,OAAO,CAAC,CAAC,CAAC;MACjF,MAAMkN,WAAW,GAAG,CAAEjH,UAAU,CAACI,aAAa,GAAG0G,WAAW,GAAI,GAAG,EAAE/M,OAAO,CAAC,CAAC,CAAC;MAE/EwM,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCH,GAAG,CAACI,IAAI,CAAC,aAAa3G,UAAU,CAACE,gBAAgB,KAAK6G,cAAc,IAAI,EAAE,EAAE,EAAEP,SAAS,CAAC;MACxFA,SAAS,IAAI,CAAC;MACdD,GAAG,CAACI,IAAI,CAAC,WAAW3G,UAAU,CAACG,cAAc,KAAK6G,YAAY,IAAI,EAAE,EAAE,EAAER,SAAS,CAAC;MAClFA,SAAS,IAAI,CAAC;MACdD,GAAG,CAACI,IAAI,CAAC,UAAU3G,UAAU,CAACI,aAAa,KAAK6G,WAAW,IAAI,EAAE,EAAE,EAAET,SAAS,CAAC;MAC/EA,SAAS,IAAI,EAAE;IACjB;;IAEA;IACA,IAAI,CAAAF,oBAAA,GAAAxF,aAAa,CAACQ,KAAK,cAAAgF,oBAAA,eAAnBA,oBAAA,CAAqBnF,MAAM,IAAIL,aAAa,CAACQ,KAAK,CAACH,MAAM,CAAC7M,MAAM,GAAG,CAAC,EAAE;MACxEiS,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCH,GAAG,CAACI,IAAI,CAAC,cAAc,EAAE,EAAE,EAAEH,SAAS,CAAC;MACvCA,SAAS,IAAI,EAAE;MAEf,MAAMU,aAAa,GAAGpG,aAAa,CAACQ,KAAK,CAACH,MAAM,CAACrM,GAAG,CAAC,CAAC0K,IAAI,EAAE2H,GAAG,KAAK,CAClEA,GAAG,GAAG,CAAC,EACP3H,IAAI,CAACnF,QAAQ,EACbmF,IAAI,CAACiE,IAAI,EACT,IAAIlJ,IAAI,CAACiF,IAAI,CAAC4H,UAAU,CAAC,CAAC5M,cAAc,CAAC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAe,CAAC,CAAC,CAChF,CAAC;MAEFtH,SAAS,CAACoT,GAAG,EAAE;QACbc,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;QAC/C1K,IAAI,EAAEuK,aAAa;QACnBI,MAAM,EAAEd,SAAS;QACjBe,MAAM,EAAE;UAAEC,GAAG,EAAE;QAAG,CAAC;QACnBC,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC;QACxBC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAAE;MACzC,CAAC,CAAC;MAEFpB,SAAS,GAAGD,GAAG,CAACsB,aAAa,CAACC,MAAM,GAAG,EAAE;IAC3C;;IAEA;IACA,IAAIhH,aAAa,CAAC9B,QAAQ,CAACmC,MAAM,IAAIL,aAAa,CAAC9B,QAAQ,CAACmC,MAAM,CAAC7M,MAAM,GAAG,CAAC,EAAE;MAC7EiS,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCH,GAAG,CAACI,IAAI,CAAC,mBAAmB,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC5CA,SAAS,IAAI,EAAE;MAEf,MAAMuB,gBAAgB,GAAGjH,aAAa,CAAC9B,QAAQ,CAACmC,MAAM,CAACrM,GAAG,CAAC,CAACyD,MAAM,EAAE4O,GAAG,KAAK,CAC1EA,GAAG,GAAG,CAAC,EACP5O,MAAM,CAACuB,QAAQ,GAAGvB,MAAM,CAACuB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,EAC7DxB,MAAM,CAACyB,QAAQ,GAAGzB,MAAM,CAACyB,QAAQ,CAACD,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,EAC5DxB,MAAM,CAAC0B,MAAM,GAAG1B,MAAM,CAAC0B,MAAM,CAACF,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,EAChDxB,MAAM,CAAC8B,QAAQ,IAAI,SAAS,EAC5B,IAAIE,IAAI,CAAChC,MAAM,CAAC+B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAe,CAAC,CAAC,CACjF,CAAC;MAEFtH,SAAS,CAACoT,GAAG,EAAE;QACbc,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QACpE1K,IAAI,EAAEoL,gBAAgB;QACtBT,MAAM,EAAEd,SAAS;QACjBe,MAAM,EAAE;UAAEC,GAAG,EAAE;QAAG,CAAC;QACnBC,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAE,CAAC;QACvBC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;QAAE;MACzC,CAAC,CAAC;MAEFpB,SAAS,GAAGD,GAAG,CAACsB,aAAa,CAACC,MAAM,GAAG,EAAE;IAC3C;;IAEA;IACA,IAAIhH,aAAa,CAAC7B,MAAM,CAACkC,MAAM,IAAIL,aAAa,CAAC7B,MAAM,CAACkC,MAAM,CAAC7M,MAAM,GAAG,CAAC,EAAE;MACzEiS,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCH,GAAG,CAACI,IAAI,CAAC,iBAAiB,EAAE,EAAE,EAAEH,SAAS,CAAC;MAC1CA,SAAS,IAAI,EAAE;MAEf,MAAMwB,cAAc,GAAGlH,aAAa,CAAC7B,MAAM,CAACkC,MAAM,CAACrM,GAAG,CAAC,CAACyD,MAAM,EAAE4O,GAAG,KAAK,CACtEA,GAAG,GAAG,CAAC,EACP5O,MAAM,CAACa,UAAU,IAAI,SAAS,EAC9Bb,MAAM,CAACuB,QAAQ,GAAGvB,MAAM,CAACuB,QAAQ,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,KAAK,EAC7DxB,MAAM,CAAC2B,UAAU,IAAI,KAAK,EAC1B3B,MAAM,CAAC8B,QAAQ,IAAI,SAAS,EAC5B,IAAIE,IAAI,CAAChC,MAAM,CAAC+B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAe,CAAC,CAAC,CACjF,CAAC;MAEFtH,SAAS,CAACoT,GAAG,EAAE;QACbc,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QAClE1K,IAAI,EAAEqL,cAAc;QACpBV,MAAM,EAAEd,SAAS;QACjBe,MAAM,EAAE;UAAEC,GAAG,EAAE;QAAG,CAAC;QACnBC,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAE,CAAC;QACvBC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;QAAE;MACzC,CAAC,CAAC;MAEFpB,SAAS,GAAGD,GAAG,CAACsB,aAAa,CAACC,MAAM,GAAG,EAAE;IAC3C;;IAEA;IACA,IAAIhH,aAAa,CAAC5B,KAAK,CAACiC,MAAM,IAAIL,aAAa,CAAC5B,KAAK,CAACiC,MAAM,CAAC7M,MAAM,GAAG,CAAC,EAAE;MACvEiS,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCH,GAAG,CAACI,IAAI,CAAC,gBAAgB,EAAE,EAAE,EAAEH,SAAS,CAAC;MACzCA,SAAS,IAAI,EAAE;MAEf,MAAMyB,aAAa,GAAGnH,aAAa,CAAC5B,KAAK,CAACiC,MAAM,CAACrM,GAAG,CAAC,CAACyD,MAAM,EAAE4O,GAAG,KAAK,CACpEA,GAAG,GAAG,CAAC,EACP5O,MAAM,CAAC4B,SAAS,IAAI,SAAS,EAC7B5B,MAAM,CAAC6B,QAAQ,GAAG7B,MAAM,CAAC6B,QAAQ,CAACL,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,EAC3DxB,MAAM,CAACe,SAAS,IAAI,SAAS,EAC7Bf,MAAM,CAAC8B,QAAQ,IAAI,SAAS,EAC5B,IAAIE,IAAI,CAAChC,MAAM,CAAC+B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;QAAEC,QAAQ,EAAE;MAAe,CAAC,CAAC,CACjF,CAAC;MAEFtH,SAAS,CAACoT,GAAG,EAAE;QACbc,IAAI,EAAE,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QACxE1K,IAAI,EAAEsL,aAAa;QACnBX,MAAM,EAAEd,SAAS;QACjBe,MAAM,EAAE;UAAEC,GAAG,EAAE;QAAG,CAAC;QACnBC,MAAM,EAAE;UAAEC,QAAQ,EAAE;QAAE,CAAC;QACvBC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QAAE;MACzC,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMM,SAAS,GAAG3B,GAAG,CAAC4B,QAAQ,CAACC,gBAAgB,CAAC,CAAC;IACjD,KAAK,IAAIxK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIsK,SAAS,EAAEtK,CAAC,EAAE,EAAE;MACnC2I,GAAG,CAAC8B,OAAO,CAACzK,CAAC,CAAC;MACd2I,GAAG,CAACE,WAAW,CAAC,EAAE,CAAC;MACnBF,GAAG,CAACG,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCH,GAAG,CAACI,IAAI,CAAC,QAAQ/I,CAAC,OAAOsK,SAAS,EAAE,EAAE,GAAG,EAAE3B,GAAG,CAAC4B,QAAQ,CAACG,QAAQ,CAACzJ,MAAM,GAAG,EAAE,EAAE;QAAE+H,KAAK,EAAE;MAAS,CAAC,CAAC;IACpG;IAEAL,GAAG,CAACgC,IAAI,CAAC,sBAAsB,CAAC;EAClC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,MAAM,GAAG,CACb,CAAC,GAAG,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,CAAC,EACvE,GAAG,CAAC3H,aAAa,CAAC9B,QAAQ,CAACmC,MAAM,IAAI,EAAE,EAAErM,GAAG,CAAC,CAACyD,MAAM,EAAE4O,GAAG,KAAK,CAC5DA,GAAG,GAAG,CAAC,EACP5O,MAAM,CAACmQ,IAAI,EACXnQ,MAAM,CAACoQ,KAAK,EACZpQ,MAAM,CAAC0B,MAAM,EACb1B,MAAM,CAAC8B,QAAQ,EACf,IAAIE,IAAI,CAAChC,MAAM,CAAC+B,SAAS,CAAC,CAACE,cAAc,CAAC,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAe,CAAC,CAAC,CACjF,CAAC,CACH;IACD,MAAMmO,EAAE,GAAGxV,IAAI,CAACyV,KAAK,CAACC,YAAY,CAACL,MAAM,CAAC;IAC1C,MAAMM,EAAE,GAAG3V,IAAI,CAACyV,KAAK,CAACG,QAAQ,CAAC,CAAC;IAChC5V,IAAI,CAACyV,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,kBAAkB,CAAC;IACxD,MAAMM,KAAK,GAAG9V,IAAI,CAAC+V,KAAK,CAACJ,EAAE,EAAE;MAAEK,QAAQ,EAAE,MAAM;MAAErN,IAAI,EAAE;IAAQ,CAAC,CAAC;IACjE1I,MAAM,CAAC,IAAI2K,IAAI,CAAC,CAACkL,KAAK,CAAC,EAAE;MAAEnN,IAAI,EAAE;IAA2B,CAAC,CAAC,EAAE,uBAAuB,CAAC;EAC1F,CAAC;EAED,oBACExI,OAAA,CAAChB,SAAS;IAAC8W,KAAK;IAAClU,SAAS,EAAC,qBAAqB;IAAA2C,QAAA,gBAE9CvE,OAAA,CAACb,IAAI;MAACyC,SAAS,EAAC,4CAA4C;MAAA2C,QAAA,gBAC1DvE,OAAA,CAACb,IAAI,CAACyG,MAAM;QAAChE,SAAS,EAAC,4BAA4B;QAAA2C,QAAA,eACjDvE,OAAA;UAAI4B,SAAS,EAAC,MAAM;UAAA2C,QAAA,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;QAACxE,SAAS,EAAC,MAAM;QAAA2C,QAAA,eACzBvE,OAAA,CAACf,GAAG;UAAC2C,SAAS,EAAC,KAAK;UAAA2C,QAAA,gBAClBvE,OAAA,CAACd,GAAG;YAAC6W,EAAE,EAAE,CAAE;YAAAxR,QAAA,eACTvE,OAAA;cAAK4B,SAAS,EAAC,gBAAgB;cAAA2C,QAAA,gBAC7BvE,OAAA;gBAAI4B,SAAS,EAAC,MAAM;gBAAA2C,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpC3E,OAAA;gBAAK4B,SAAS,EAAC,iBAAiB;gBAAA2C,QAAA,gBAC9BvE,OAAA;kBAAK4B,SAAS,EAAC,cAAc;kBAAA2C,QAAA,eAC3BvE,OAAA,CAACV,IAAI,CAAC0W,KAAK;oBAAAzR,QAAA,gBACTvE,OAAA,CAACV,IAAI,CAAC2W,KAAK;sBAACrU,SAAS,EAAC,YAAY;sBAAA2C,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1D3E,OAAA,CAACV,IAAI,CAAC4W,OAAO;sBACX1N,IAAI,EAAC,MAAM;sBACX+J,KAAK,EAAEnE,SAAU;sBACjB+H,QAAQ,EAAG9K,CAAC,IAAKgD,YAAY,CAAChD,CAAC,CAACnI,MAAM,CAACqP,KAAK,CAAE;sBAC9CnL,IAAI,EAAC;oBAAI;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3E,OAAA;kBAAK4B,SAAS,EAAC,cAAc;kBAAA2C,QAAA,eAC3BvE,OAAA,CAACV,IAAI,CAAC0W,KAAK;oBAAAzR,QAAA,gBACTvE,OAAA,CAACV,IAAI,CAAC2W,KAAK;sBAACrU,SAAS,EAAC,YAAY;sBAAA2C,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxD3E,OAAA,CAACV,IAAI,CAAC4W,OAAO;sBACX1N,IAAI,EAAC,MAAM;sBACX+J,KAAK,EAAEjE,OAAQ;sBACf6H,QAAQ,EAAG9K,CAAC,IAAKkD,UAAU,CAAClD,CAAC,CAACnI,MAAM,CAACqP,KAAK,CAAE;sBAC5CnL,IAAI,EAAC;oBAAI;sBAAA5C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3E,OAAA;kBAAK4B,SAAS,EAAC,gBAAgB;kBAAA2C,QAAA,gBAC7BvE,OAAA,CAACT,MAAM;oBACL6H,IAAI,EAAC,IAAI;oBACTD,OAAO,EAAC,SAAS;oBACjBE,OAAO,EAAE0K,qBAAsB;oBAC/BnG,QAAQ,EAAE,CAACwC,SAAS,IAAI,CAACE,OAAQ;oBAAA/J,QAAA,EAClC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3E,OAAA,CAACT,MAAM;oBACL6H,IAAI,EAAC,IAAI;oBACTD,OAAO,EAAC,mBAAmB;oBAC3BE,OAAO,EAAE4K,qBAAsB;oBAC/BrG,QAAQ,EAAE,CAAC4C,iBAAkB;oBAAAjK,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL6J,iBAAiB,iBAChBxO,OAAA;gBAAK4B,SAAS,EAAC,qCAAqC;gBAAA2C,QAAA,eAClDvE,OAAA;kBAAAuE,QAAA,GAAO,oBAAkB,EAAC,IAAIyC,IAAI,CAACoH,SAAS,CAAC,CAACkF,kBAAkB,CAAC,CAAC,EAAC,MAAI,EAAC,IAAItM,IAAI,CAACsH,OAAO,CAAC,CAACgF,kBAAkB,CAAC,CAAC;gBAAA;kBAAA9O,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3E,OAAA,CAACd,GAAG;YAAC6W,EAAE,EAAE,CAAE;YAAAxR,QAAA,eACTvE,OAAA;cAAK4B,SAAS,EAAC,gBAAgB;cAAA2C,QAAA,gBAC7BvE,OAAA;gBAAI4B,SAAS,EAAC,MAAM;gBAAA2C,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrC3E,OAAA;gBAAK4B,SAAS,EAAC,iBAAiB;gBAAA2C,QAAA,gBAC9BvE,OAAA;kBAAK4B,SAAS,EAAC,cAAc;kBAAA2C,QAAA,eAC3BvE,OAAA,CAACV,IAAI,CAAC0W,KAAK;oBAAAzR,QAAA,gBACTvE,OAAA,CAACV,IAAI,CAAC2W,KAAK;sBAACrU,SAAS,EAAC,YAAY;sBAAA2C,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC3D3E,OAAA,CAACV,IAAI,CAAC8W,MAAM;sBACV7D,KAAK,EAAE3D,YAAa;sBACpBuH,QAAQ,EAAE7D,gBAAiB;sBAC3BlL,IAAI,EAAC,IAAI;sBAAA7C,QAAA,gBAETvE,OAAA;wBAAQuS,KAAK,EAAC,EAAE;wBAAAhO,QAAA,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EAClC+J,SAAS,CAACnN,GAAG,CAAC,CAAC0K,IAAI,EAAEyG,KAAK,kBACzB1S,OAAA;wBAAoBuS,KAAK,EAAEtG,IAAI,CAACnF,QAAS;wBAAAvC,QAAA,GACtC0H,IAAI,CAACnF,QAAQ,EAAC,IAAE,EAACmF,IAAI,CAACiE,IAAI,EAAC,GAC9B;sBAAA,GAFawC,KAAK;wBAAAlO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEV,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN3E,OAAA;kBAAK4B,SAAS,EAAC,gBAAgB;kBAAA2C,QAAA,gBAC7BvE,OAAA,CAACT,MAAM;oBACL6H,IAAI,EAAC,IAAI;oBACTD,OAAO,EAAC,SAAS;oBACjBE,OAAO,EAAE+K,qBAAsB;oBAAA7N,QAAA,EAChC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3E,OAAA,CAACT,MAAM;oBACL6H,IAAI,EAAC,IAAI;oBACTD,OAAO,EAAC,mBAAmB;oBAC3BE,OAAO,EAAEgL,qBAAsB;oBAC/BzG,QAAQ,EAAE,CAACkD,iBAAkB;oBAAAvK,QAAA,EAC9B;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLmK,iBAAiB,iBAChB9O,OAAA;gBAAK4B,SAAS,EAAC,qCAAqC;gBAAA2C,QAAA,eAClDvE,OAAA;kBAAAuE,QAAA,GAAO,yBAAuB,EAACqK,YAAY;gBAAA;kBAAApK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAENC,OAAO,gBACN5E,OAAA;MAAK4B,SAAS,EAAC,aAAa;MAAA2C,QAAA,eAC1BvE,OAAA;QAAK4B,SAAS,EAAC,6BAA6B;QAACsO,IAAI,EAAC,QAAQ;QAAA3L,QAAA,eACxDvE,OAAA;UAAM4B,SAAS,EAAC,iBAAiB;UAAA2C,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,GACJ1B,KAAK,gBACPjD,OAAA;MAAK4B,SAAS,EAAC,wBAAwB;MAAA2C,QAAA,EAAEtB;IAAK;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,gBAErD3E,OAAA,CAAAE,SAAA;MAAAqE,QAAA,gBAEEvE,OAAA,CAACb,IAAI;QAACyC,SAAS,EAAC,+BAA+B;QAAA2C,QAAA,eAC7CvE,OAAA,CAACb,IAAI,CAACiH,IAAI;UAACxE,SAAS,EAAC,KAAK;UAAA2C,QAAA,eACxBvE,OAAA,CAACZ,IAAI;YACHiX,SAAS,EAAEjH,SAAU;YACrBkH,QAAQ,EAAGC,CAAC,IAAKlH,YAAY,CAACkH,CAAC,CAAE;YACjC3U,SAAS,EAAC,gBAAgB;YAAA2C,QAAA,gBAG1BvE,OAAA,CAACX,GAAG;cAACmX,QAAQ,EAAC,WAAW;cAACC,KAAK,EAAC,gBAAgB;cAAAlS,QAAA,eAC9CvE,OAAA;gBAAK4B,SAAS,EAAC,KAAK;gBAAA2C,QAAA,gBAElBvE,OAAA,CAACf,GAAG;kBAAC2C,SAAS,EAAC,UAAU;kBAAA2C,QAAA,gBACvBvE,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,2CAA2C;sBAAA2C,QAAA,eACzDvE,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,kBAAkB;wBAAA2C,QAAA,gBACrCvE,OAAA;0BAAI4B,SAAS,EAAC,iBAAiB;0BAAA2C,QAAA,EAAC;wBAAQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7C3E,OAAA;0BAAI4B,SAAS,EAAC,mBAAmB;0BAAA2C,QAAA,EAAEkI,UAAU,CAACE;wBAAgB;0BAAAnI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN3E,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,2CAA2C;sBAAA2C,QAAA,eACzDvE,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,kBAAkB;wBAAA2C,QAAA,gBACrCvE,OAAA;0BAAI4B,SAAS,EAAC,iBAAiB;0BAAA2C,QAAA,EAAC;wBAAM;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3C3E,OAAA;0BAAI4B,SAAS,EAAC,mBAAmB;0BAAA2C,QAAA,EAAEkI,UAAU,CAACG;wBAAc;0BAAApI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN3E,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,2CAA2C;sBAAA2C,QAAA,eACzDvE,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,kBAAkB;wBAAA2C,QAAA,gBACrCvE,OAAA;0BAAI4B,SAAS,EAAC,iBAAiB;0BAAA2C,QAAA,EAAC;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1C3E,OAAA;0BAAI4B,SAAS,EAAC,mBAAmB;0BAAA2C,QAAA,EAAEkI,UAAU,CAACI;wBAAa;0BAAArI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN3E,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,2CAA2C;sBAAA2C,QAAA,eACzDvE,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,kBAAkB;wBAAA2C,QAAA,gBACrCvE,OAAA;0BAAI4B,SAAS,EAAC,iBAAiB;0BAAA2C,QAAA,EAAC;wBAAK;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC1C3E,OAAA;0BAAI4B,SAAS,EAAC,mBAAmB;0BAAA2C,QAAA,EAAEkI,UAAU,CAACK;wBAAU;0BAAAtI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3E,OAAA,CAACf,GAAG;kBAAC2C,SAAS,EAAC,UAAU;kBAAA2C,QAAA,gBACvBvE,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,0BAA0B;sBAAA2C,QAAA,gBACxCvE,OAAA,CAACb,IAAI,CAACyG,MAAM;wBAAChE,SAAS,EAAC,4BAA4B;wBAAA2C,QAAA,eACjDvE,OAAA;0BAAI4B,SAAS,EAAC,MAAM;0BAAA2C,QAAA,EAAC;wBAAsB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrC,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,MAAM;wBAAA2C,QAAA,eACzBvE,OAAA,CAACP,cAAc;0BACbsK,IAAI,EAAE,CACJ;4BACE2M,CAAC,EAAE3J,UAAU,CAACE,IAAI;4BAClB0J,CAAC,EAAE5J,UAAU,CAACG,MAAM;4BACpB1E,IAAI,EAAE,SAAS;4BACfoO,IAAI,EAAE,eAAe;4BACrBC,MAAM,EAAE;8BAAEC,KAAK,EAAE;4BAAU;0BAC7B,CAAC,CACD;0BACFC,MAAM,EAAE;4BACNC,KAAK,EAAE;8BAAEP,KAAK,EAAE;4BAAM,CAAC;4BACvBQ,KAAK,EAAE;8BAAER,KAAK,EAAE;4BAAkB;0BACpC;wBAAE;0BAAAjS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAEN3E,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,0BAA0B;sBAAA2C,QAAA,gBACxCvE,OAAA,CAACb,IAAI,CAACyG,MAAM;wBAAChE,SAAS,EAAC,4BAA4B;wBAAA2C,QAAA,eACjDvE,OAAA;0BAAI4B,SAAS,EAAC,MAAM;0BAAA2C,QAAA,EAAC;wBAAc;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,MAAM;wBAAA2C,QAAA,eACzBvE,OAAA,CAACP,cAAc;0BACbsK,IAAI,EAAE,CACJ;4BACEvB,IAAI,EAAE,KAAK;4BACXkO,CAAC,EAAExH,oBAAoB,CAAC7B,KAAK;4BAC7BsJ,CAAC,EAAEzH,oBAAoB,CAAC5B,MAAM;4BAC9BuJ,MAAM,EAAE;8BACNC,KAAK,EAAE5H,oBAAoB,CAAC7B,KAAK,CAAC9L,GAAG,CAACiH,IAAI,IAAI;gCAC5C,IAAIA,IAAI,CAAC1H,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,SAAS;gCAC9C,IAAI0H,IAAI,CAAC1H,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,SAAS;gCAC5C,IAAI0H,IAAI,CAAC1H,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,SAAS;gCAC3C,OAAO,SAAS;8BAClB,CAAC;4BACH;0BACF,CAAC,CACD;0BACFiW,MAAM,EAAE;4BACNC,KAAK,EAAE;8BACLP,KAAK,EAAE,YAAY;8BACnBS,SAAS,EAAE,CAAC,EAAE;8BACdC,UAAU,EAAE;4BACd,CAAC;4BACDF,KAAK,EAAE;8BAAER,KAAK,EAAE;4BAAQ,CAAC;4BACzBzC,MAAM,EAAE;8BAAEoD,CAAC,EAAE,EAAE;8BAAEC,CAAC,EAAE,EAAE;8BAAEC,CAAC,EAAE,EAAE;8BAAEC,CAAC,EAAE;4BAAG;0BACvC,CAAE;0BACFC,UAAU,EAAE,IAAK;0BACjBC,WAAW,EAAE,CACX;4BACEC,KAAK,EAAE,UAAU;4BACjBZ,KAAK,EAAE,SAAS;4BAChBa,OAAO,EAAE3I,aAAa,CAACvD,QAAQ;4BAC/B0K,QAAQ,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,UAAU;0BACrD,CAAC,EACD;4BACE8E,KAAK,EAAE,QAAQ;4BACfZ,KAAK,EAAE,SAAS;4BAChBa,OAAO,EAAE3I,aAAa,CAACtD,MAAM;4BAC7ByK,QAAQ,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,QAAQ;0BACnD,CAAC,EACD;4BACE8E,KAAK,EAAE,OAAO;4BACdZ,KAAK,EAAE,SAAS;4BAChBa,OAAO,EAAE3I,aAAa,CAACrD,KAAK;4BAC5BwK,QAAQ,EAAEA,CAAA,KAAMvD,wBAAwB,CAAC,OAAO;0BAClD,CAAC,CACD;0BACFhR,SAAS,EAAC;wBAAgB;0BAAA4C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3E,OAAA,CAACf,GAAG;kBAAC2C,SAAS,EAAC,MAAM;kBAAA2C,QAAA,eACnBvE,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,EAAG;oBAAApB,QAAA,eACVvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,0BAA0B;sBAAA2C,QAAA,gBACxCvE,OAAA,CAACb,IAAI,CAACyG,MAAM;wBAAChE,SAAS,EAAC,4BAA4B;wBAAA2C,QAAA,eACjDvE,OAAA;0BAAI4B,SAAS,EAAC,MAAM;0BAAA2C,QAAA,EAAC;wBAA2B;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,MAAM;wBAAA2C,QAAA,eACzBvE,OAAA,CAACP,cAAc;0BACbsK,IAAI,EAAE,CACJ;4BACEvB,IAAI,EAAE,KAAK;4BACXoP,MAAM,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;4BACvCC,MAAM,EAAE,CACNpL,UAAU,CAACE,gBAAgB,EAC3BF,UAAU,CAACG,cAAc,EACzBH,UAAU,CAACI,aAAa,CACzB;4BACDgK,MAAM,EAAE;8BACNiB,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;4BAC1C,CAAC;4BACDC,QAAQ,EAAE,eAAe;4BACzBC,qBAAqB,EAAE;0BACzB,CAAC,CACD;0BACFjB,MAAM,EAAE;4BACNzL,MAAM,EAAE;0BACV,CAAE;0BACF2M,UAAU,EAAE;wBAAK;0BAAAzT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3E,OAAA,CAACf,GAAG;kBAAC2C,SAAS,EAAC,MAAM;kBAAA2C,QAAA,eACnBvE,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,EAAG;oBAAApB,QAAA,eACVvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,0BAA0B;sBAAA2C,QAAA,gBACxCvE,OAAA,CAACb,IAAI,CAACyG,MAAM;wBAAChE,SAAS,EAAC,4BAA4B;wBAAA2C,QAAA,eACjDvE,OAAA;0BAAI4B,SAAS,EAAC,MAAM;0BAAA2C,QAAA,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,MAAM;wBAAA2C,QAAA,eACzBvE,OAAA,CAACZ,IAAI;0BAAC8Y,gBAAgB,EAAC,UAAU;0BAACtW,SAAS,EAAC,MAAM;0BAAA2C,QAAA,gBAChDvE,OAAA,CAACX,GAAG;4BAACmX,QAAQ,EAAC,UAAU;4BAACC,KAAK,EAAE,aAAalJ,aAAa,CAAC9B,QAAQ,CAACmC,MAAM,CAAC7M,MAAM,GAAI;4BAAAwD,QAAA,eACnFvE,OAAA;8BAAK6B,KAAK,EAAE;gCAAEwE,SAAS,EAAE,OAAO;gCAAE8R,SAAS,EAAE,MAAM;gCAAEC,YAAY,EAAE;8BAAO,CAAE;8BAAA7T,QAAA,eAC1EvE,OAAA,CAACf,GAAG;gCAAAsF,QAAA,EACDgJ,aAAa,CAAC9B,QAAQ,CAACmC,MAAM,CAACrM,GAAG,CAAC,CAAC8W,OAAO,EAAE3F,KAAK,kBAChD1S,OAAA,CAAC+E,SAAS;kCAERC,MAAM,EAAEqT,OAAQ;kCAChBpT,UAAU,EAAC,UAAU;kCACrBC,WAAW,EAAC;gCAAY,GAHnB,WAAWmT,OAAO,CAACpS,UAAU,IAAIoS,OAAO,CAAC/Q,QAAQ,IAAIoL,KAAK,EAAE;kCAAAlO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAIlE,CACF;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACN3E,OAAA,CAACX,GAAG;4BAACmX,QAAQ,EAAC,QAAQ;4BAACC,KAAK,EAAE,WAAWlJ,aAAa,CAAC7B,MAAM,CAACkC,MAAM,CAAC7M,MAAM,GAAI;4BAAAwD,QAAA,eAC7EvE,OAAA;8BAAK6B,KAAK,EAAE;gCAAEwE,SAAS,EAAE,OAAO;gCAAE8R,SAAS,EAAE,MAAM;gCAAEC,YAAY,EAAE;8BAAO,CAAE;8BAAA7T,QAAA,eAC1EvE,OAAA,CAACf,GAAG;gCAAAsF,QAAA,EACDgJ,aAAa,CAAC7B,MAAM,CAACkC,MAAM,CAACrM,GAAG,CAAC,CAAC+W,KAAK,EAAE5F,KAAK,kBAC5C1S,OAAA,CAAC+E,SAAS;kCAERC,MAAM,EAAEsT,KAAM;kCACdrT,UAAU,EAAC,QAAQ;kCACnBC,WAAW,EAAC;gCAAU,GAHjB,SAASoT,KAAK,CAACxS,QAAQ,IAAIwS,KAAK,CAAChR,QAAQ,IAAIoL,KAAK,EAAE;kCAAAlO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAI1D,CACF;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACN3E,OAAA,CAACX,GAAG;4BAACmX,QAAQ,EAAC,OAAO;4BAACC,KAAK,EAAE,UAAUlJ,aAAa,CAAC5B,KAAK,CAACiC,MAAM,CAAC7M,MAAM,GAAI;4BAAAwD,QAAA,eAC1EvE,OAAA;8BAAK6B,KAAK,EAAE;gCAAEwE,SAAS,EAAE,OAAO;gCAAE8R,SAAS,EAAE,MAAM;gCAAEC,YAAY,EAAE;8BAAO,CAAE;8BAAA7T,QAAA,eAC1EvE,OAAA,CAACf,GAAG;gCAAAsF,QAAA,EACDgJ,aAAa,CAAC5B,KAAK,IAAI4B,aAAa,CAAC5B,KAAK,CAACiC,MAAM,IAAIL,aAAa,CAAC5B,KAAK,CAACiC,MAAM,CAAC7M,MAAM,GAAG,CAAC,GACzFwM,aAAa,CAAC5B,KAAK,CAACiC,MAAM,CAACrM,GAAG,CAAC,CAACgX,IAAI,EAAE7F,KAAK,kBACzC1S,OAAA,CAAC+E,SAAS;kCAERC,MAAM,EAAEuT,IAAK;kCACbtT,UAAU,EAAC,OAAO;kCAClBC,WAAW,EAAC;gCAAS,GAHhB,QAAQqT,IAAI,CAACvS,OAAO,IAAIuS,IAAI,CAACjR,QAAQ,IAAIoL,KAAK,EAAE;kCAAAlO,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAItD,CACF,CAAC,gBAEF3E,OAAA,CAACd,GAAG;kCAAAqF,QAAA,eACFvE,OAAA;oCAAK4B,SAAS,EAAC,sBAAsB;oCAAA2C,QAAA,EAAC;kCAEtC;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAK;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH;8BACN;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN3E,OAAA,CAACf,GAAG;kBAAC2C,SAAS,EAAC,MAAM;kBAAA2C,QAAA,eACnBvE,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,EAAG;oBAAApB,QAAA,eACVvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,0BAA0B;sBAAA2C,QAAA,gBACxCvE,OAAA,CAACb,IAAI,CAACyG,MAAM;wBAAChE,SAAS,EAAC,yBAAyB;wBAAA2C,QAAA,eAC9CvE,OAAA;0BAAI4B,SAAS,EAAC,MAAM;0BAAA2C,QAAA,GAAC,wBAAsB,EAAC,EAAA4H,qBAAA,GAAAoB,aAAa,CAACU,MAAM,cAAA9B,qBAAA,wBAAAC,sBAAA,GAApBD,qBAAA,CAAsByB,MAAM,cAAAxB,sBAAA,uBAA5BA,sBAAA,CAA8BrL,MAAM,KAAI,CAAC,EAAC,GAAC;wBAAA;0BAAAyD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjF,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,MAAM;wBAAA2C,QAAA,EACxB,CAAA8H,sBAAA,GAAAkB,aAAa,CAACU,MAAM,cAAA5B,sBAAA,eAApBA,sBAAA,CAAsBuB,MAAM,IAAIL,aAAa,CAACU,MAAM,CAACL,MAAM,CAAC7M,MAAM,GAAG,CAAC,gBACrEf,OAAA;0BAAK6B,KAAK,EAAE;4BAAEwE,SAAS,EAAE,OAAO;4BAAE8R,SAAS,EAAE,MAAM;4BAAEC,YAAY,EAAE;0BAAO,CAAE;0BAAA7T,QAAA,eAC1EvE,OAAA,CAACf,GAAG;4BAAAsF,QAAA,EACDgJ,aAAa,CAACU,MAAM,CAACL,MAAM,CAACrM,GAAG,CAAC,CAACoG,KAAK,EAAE+K,KAAK,kBAC5C1S,OAAA,CAAC0H,SAAS;8BAERC,KAAK,EAAEA;4BAAM,GADR,SAASA,KAAK,CAACK,QAAQ,IAAI0K,KAAK,EAAE;8BAAAlO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAExC,CACF;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAEN3E,OAAA;0BAAK4B,SAAS,EAAC,sBAAsB;0BAAA2C,QAAA,EAAC;wBAEtC;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK;sBACN;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3E,OAAA,CAACX,GAAG;cAACmX,QAAQ,EAAC,KAAK;cAACC,KAAK,EAAC,iBAAiB;cAAAlS,QAAA,eACzCvE,OAAA;gBAAK4B,SAAS,EAAC,KAAK;gBAAA2C,QAAA,eAClBvE,OAAA,CAACN,SAAS;kBAACuM,IAAI,EAAEA;gBAAK;kBAAAzH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3E,OAAA,CAACX,GAAG;cAACmX,QAAQ,EAAC,OAAO;cAACC,KAAK,EAAC,gBAAgB;cAAAlS,QAAA,eAC1CvE,OAAA;gBAAK4B,SAAS,EAAC,KAAK;gBAAA2C,QAAA,eAClBvE,OAAA,CAACf,GAAG;kBAAC2C,SAAS,EAAC,KAAK;kBAAA2C,QAAA,gBAClBvE,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,0BAA0B;sBAAA2C,QAAA,gBACxCvE,OAAA,CAACb,IAAI,CAACyG,MAAM;wBAAChE,SAAS,EAAC,4BAA4B;wBAAA2C,QAAA,eACjDvE,OAAA;0BAAI4B,SAAS,EAAC,MAAM;0BAAA2C,QAAA,EAAC;wBAAyB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,MAAM;wBAAA2C,QAAA,eACzBvE,OAAA,CAACP,cAAc;0BACbsK,IAAI,EAAE,CACJ;4BACEvB,IAAI,EAAE,KAAK;4BACXoP,MAAM,EAAE9T,MAAM,CAAC0U,IAAI,CAAC,EAAAlM,qBAAA,GAAAiB,aAAa,CAACQ,KAAK,cAAAzB,qBAAA,uBAAnBA,qBAAA,CAAqB0B,OAAO,KAAI,CAAC,CAAC,CAAC;4BACvD6J,MAAM,EAAE/T,MAAM,CAAC+T,MAAM,CAAC,EAAAtL,qBAAA,GAAAgB,aAAa,CAACQ,KAAK,cAAAxB,qBAAA,uBAAnBA,qBAAA,CAAqByB,OAAO,KAAI,CAAC,CAAC,CAAC;4BACzD6I,MAAM,EAAE;8BACNiB,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;4BACrD,CAAC;4BACDC,QAAQ,EAAE,eAAe;4BACzBC,qBAAqB,EAAE;0BACzB,CAAC,CACD;0BACFC,UAAU,EAAE;wBAAK;0BAAAzT,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN3E,OAAA,CAACd,GAAG;oBAACyG,EAAE,EAAE,CAAE;oBAAApB,QAAA,eACTvE,OAAA,CAACb,IAAI;sBAACyC,SAAS,EAAC,0BAA0B;sBAAA2C,QAAA,gBACxCvE,OAAA,CAACb,IAAI,CAACyG,MAAM;wBAAChE,SAAS,EAAC,4BAA4B;wBAAA2C,QAAA,eACjDvE,OAAA;0BAAI4B,SAAS,EAAC,MAAM;0BAAA2C,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CAAC,eACd3E,OAAA,CAACb,IAAI,CAACiH,IAAI;wBAACxE,SAAS,EAAC,MAAM;wBAAA2C,QAAA,eACzBvE,OAAA;0BAAK4B,SAAS,EAAC,kBAAkB;0BAAA2C,QAAA,eAC/BvE,OAAA;4BAAO4B,SAAS,EAAC,4BAA4B;4BAAA2C,QAAA,gBAC3CvE,OAAA;8BAAAuE,QAAA,eACEvE,OAAA;gCAAAuE,QAAA,gBACEvE,OAAA;kCAAAuE,QAAA,EAAI;gCAAQ;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eACjB3E,OAAA;kCAAAuE,QAAA,EAAI;gCAAI;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC,eACb3E,OAAA;kCAAAuE,QAAA,EAAI;gCAAU;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACjB;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA,CAAC,eACR3E,OAAA;8BAAAuE,QAAA,EACG,CAAAiI,qBAAA,GAAAe,aAAa,CAACQ,KAAK,cAAAvB,qBAAA,eAAnBA,qBAAA,CAAqBoB,MAAM,IAAIL,aAAa,CAACQ,KAAK,CAACH,MAAM,CAAC7M,MAAM,GAAG,CAAC,GACnEwM,aAAa,CAACQ,KAAK,CAACH,MAAM,CAACrM,GAAG,CAAC,CAAC0K,IAAI,EAAEyG,KAAK,kBACzC1S,OAAA;gCAAAuE,QAAA,gBACEvE,OAAA;kCAAAuE,QAAA,EAAK0H,IAAI,CAACnF;gCAAQ;kCAAAtC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,eACxB3E,OAAA;kCAAAuE,QAAA,eACEvE,OAAA;oCAAM4B,SAAS,EAAE,YACfqK,IAAI,CAACiE,IAAI,KAAK,OAAO,GAAG,QAAQ,GAChCjE,IAAI,CAACiE,IAAI,KAAK,SAAS,GAAG,SAAS,GACnC,SAAS,EACR;oCAAA3L,QAAA,EACA0H,IAAI,CAACiE;kCAAI;oCAAA1L,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACN;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACL,CAAC,eACL3E,OAAA;kCAAAuE,QAAA,EAAK,IAAIyC,IAAI,CAACiF,IAAI,CAAC4H,UAAU,CAAC,CAAC5M,cAAc,CAAC,OAAO,EAAE;oCAAEC,QAAQ,EAAE;kCAAe,CAAC;gCAAC;kCAAA1C,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC;8BAAA,GAXnF,QAAQ+N,KAAK,EAAE;gCAAAlO,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAYpB,CACL,CAAC,gBAEF3E,OAAA;gCAAAuE,QAAA,eACEvE,OAAA;kCAAIyY,OAAO,EAAC,GAAG;kCAAC7W,SAAS,EAAC,aAAa;kCAAA2C,QAAA,EAAC;gCAAuB;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAI;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAClE;4BACL;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACI,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGP3E,OAAA;QAAK4B,SAAS,EAAC,iCAAiC;QAAA2C,QAAA,gBAC9CvE,OAAA,CAACT,MAAM;UAAC4H,OAAO,EAAC,gBAAgB;UAACC,IAAI,EAAC,IAAI;UAACxF,SAAS,EAAC,MAAM;UAACyF,OAAO,EAAEyL,iBAAkB;UAAAvO,QAAA,gBACrFvE,OAAA;YAAG4B,SAAS,EAAC;UAAsB;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,gBAC1C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT3E,OAAA,CAACT,MAAM;UAAC4H,OAAO,EAAC,iBAAiB;UAACC,IAAI,EAAC,IAAI;UAACC,OAAO,EAAE4N,mBAAoB;UAAA1Q,QAAA,gBACvEvE,OAAA;YAAG4B,SAAS,EAAC;UAAwB;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBAC5C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB;AAACuH,GAAA,CAx+BQF,SAAS;AAAA0M,GAAA,GAAT1M,SAAS;AA0+BlB,eAAeA,SAAS;AAAC,IAAAlH,EAAA,EAAA2C,GAAA,EAAAsE,GAAA,EAAA2M,GAAA;AAAAC,YAAA,CAAA7T,EAAA;AAAA6T,YAAA,CAAAlR,GAAA;AAAAkR,YAAA,CAAA5M,GAAA;AAAA4M,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}